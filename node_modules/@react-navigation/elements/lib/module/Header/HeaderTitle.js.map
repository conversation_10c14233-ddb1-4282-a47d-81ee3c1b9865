{"version": 3, "names": ["useTheme", "Animated", "Platform", "StyleSheet", "jsx", "_jsx", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tintColor", "style", "rest", "colors", "fonts", "Text", "role", "numberOfLines", "color", "undefined", "text", "select", "ios", "bold", "default", "medium", "styles", "title", "create", "fontSize", "android"], "sourceRoot": "../../../src", "sources": ["Header/HeaderTitle.tsx"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,0BAA0B;AACnD,SACEC,QAAQ,EACRC,QAAQ,EAERC,UAAU,QAGL,cAAc;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAQtB,OAAO,SAASC,WAAWA,CAAC;EAAEC,SAAS;EAAEC,KAAK;EAAE,GAAGC;AAAY,CAAC,EAAE;EAChE,MAAM;IAAEC,MAAM;IAAEC;EAAM,CAAC,GAAGX,QAAQ,CAAC,CAAC;EAEpC,oBACEK,IAAA,CAACJ,QAAQ,CAACW,IAAI;IACZC,IAAI,EAAC,SAAS;IACd,cAAW,GAAG;IACdC,aAAa,EAAE,CAAE;IAAA,GACbL,IAAI;IACRD,KAAK,EAAE,CACL;MAAEO,KAAK,EAAER,SAAS,KAAKS,SAAS,GAAGN,MAAM,CAACO,IAAI,GAAGV;IAAU,CAAC,EAC5DL,QAAQ,CAACgB,MAAM,CAAC;MAAEC,GAAG,EAAER,KAAK,CAACS,IAAI;MAAEC,OAAO,EAAEV,KAAK,CAACW;IAAO,CAAC,CAAC,EAC3DC,MAAM,CAACC,KAAK,EACZhB,KAAK;EACL,CACH,CAAC;AAEN;AAEA,MAAMe,MAAM,GAAGpB,UAAU,CAACsB,MAAM,CAAC;EAC/BD,KAAK,EAAEtB,QAAQ,CAACgB,MAAM,CAAC;IACrBC,GAAG,EAAE;MACHO,QAAQ,EAAE;IACZ,CAAC;IACDC,OAAO,EAAE;MACPD,QAAQ,EAAE;IACZ,CAAC;IACDL,OAAO,EAAE;MACPK,QAAQ,EAAE;IACZ;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}