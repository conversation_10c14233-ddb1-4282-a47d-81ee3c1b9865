{"version": 3, "names": ["Easing", "FadeSpec", "animation", "config", "duration", "easing", "in", "linear", "ShiftSpec", "inOut", "ease"], "sourceRoot": "../../../src", "sources": ["TransitionConfigs/TransitionSpecs.tsx"], "mappings": ";;AAAA,SAASA,MAAM,QAAQ,cAAc;AAIrC,OAAO,MAAMC,QAAwB,GAAG;EACtCC,SAAS,EAAE,QAAQ;EACnBC,MAAM,EAAE;IACNC,QAAQ,EAAE,GAAG;IACbC,MAAM,EAAEL,MAAM,CAACM,EAAE,CAACN,MAAM,CAACO,MAAM;EACjC;AACF,CAAC;AAED,OAAO,MAAMC,SAAyB,GAAG;EACvCN,SAAS,EAAE,QAAQ;EACnBC,MAAM,EAAE;IACNC,QAAQ,EAAE,GAAG;IACbC,MAAM,EAAEL,MAAM,CAACS,KAAK,CAACT,MAAM,CAACU,IAAI;EAClC;AACF,CAAC", "ignoreList": []}