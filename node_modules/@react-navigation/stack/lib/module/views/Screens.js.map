{"version": 3, "names": ["React", "View", "jsx", "_jsx", "Screens", "require", "e", "MaybeScreenContainer", "enabled", "rest", "ScreenContainer", "MaybeScreen", "active", "Screen", "activityState"], "sourceRoot": "../../../src", "sources": ["views/Screens.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAAmBC,IAAI,QAAwB,cAAc;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAE9D,IAAIC,OAA0D;AAE9D,IAAI;EACFA,OAAO,GAAGC,OAAO,CAAC,sBAAsB,CAAC;AAC3C,CAAC,CAAC,OAAOC,CAAC,EAAE;EACV;AAAA;AAGF,OAAO,MAAMC,oBAAoB,GAAGA,CAAC;EACnCC,OAAO;EACP,GAAGC;AAIL,CAAC,KAAK;EACJ,IAAIL,OAAO,IAAI,IAAI,EAAE;IACnB,oBAAOD,IAAA,CAACC,OAAO,CAACM,eAAe;MAACF,OAAO,EAAEA,OAAQ;MAAA,GAAKC;IAAI,CAAG,CAAC;EAChE;EAEA,oBAAON,IAAA,CAACF,IAAI;IAAA,GAAKQ;EAAI,CAAG,CAAC;AAC3B,CAAC;AAED,OAAO,MAAME,WAAW,GAAGA,CAAC;EAC1BH,OAAO;EACPI,MAAM;EACN,GAAGH;AAQL,CAAC,KAAK;EACJ,IAAIL,OAAO,IAAI,IAAI,EAAE;IACnB,oBACED,IAAA,CAACC,OAAO,CAACS,MAAM;MAACL,OAAO,EAAEA,OAAQ;MAACM,aAAa,EAAEF,MAAO;MAAA,GAAKH;IAAI,CAAG,CAAC;EAEzE;EAEA,oBAAON,IAAA,CAACF,IAAI;IAAA,GAAKQ;EAAI,CAAG,CAAC;AAC3B,CAAC", "ignoreList": []}