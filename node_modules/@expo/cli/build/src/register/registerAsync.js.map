{"version": 3, "sources": ["../../../src/register/registerAsync.ts"], "sourcesContent": ["import { env } from '../utils/env';\nimport { CommandError } from '../utils/errors';\nimport { isInteractive } from '../utils/interactive';\nimport { learnMore } from '../utils/link';\nimport { openBrowserAsync } from '../utils/open';\nimport { ora } from '../utils/ora';\n\nexport async function registerAsync() {\n  if (!isInteractive()) {\n    throw new CommandError(\n      'NON_INTERACTIVE',\n      `Cannot register an account in CI. Use the EXPO_TOKEN environment variable to authenticate in CI (${learnMore(\n        'https://docs.expo.dev/accounts/programmatic-access/'\n      )})`\n    );\n  } else if (env.EXPO_OFFLINE) {\n    throw new CommandError('OFFLINE', `Cannot register an account in offline-mode`);\n  }\n\n  const registrationUrl = `https://expo.dev/signup`;\n  const failedMessage = `Unable to open a web browser. Register an account at: ${registrationUrl}`;\n  const spinner = ora(`Opening ${registrationUrl}`).start();\n  try {\n    const opened = await openBrowserAsync(registrationUrl);\n\n    if (opened) {\n      spinner.succeed(`Opened ${registrationUrl}`);\n    } else {\n      spinner.fail(failedMessage);\n    }\n  } catch (error) {\n    spinner.fail(failedMessage);\n    throw error;\n  }\n}\n"], "names": ["registerAsync", "isInteractive", "CommandError", "learnMore", "env", "EXPO_OFFLINE", "registrationUrl", "failedMessage", "spinner", "ora", "start", "opened", "openBrowserAsync", "succeed", "fail", "error"], "mappings": ";;;;+BAOsBA;;;eAAAA;;;qBAPF;wBACS;6BACC;sBACJ;sBACO;qBACb;AAEb,eAAeA;IACpB,IAAI,CAACC,IAAAA,0BAAa,KAAI;QACpB,MAAM,IAAIC,oBAAY,CACpB,mBACA,CAAC,iGAAiG,EAAEC,IAAAA,eAAS,EAC3G,uDACA,CAAC,CAAC;IAER,OAAO,IAAIC,QAAG,CAACC,YAAY,EAAE;QAC3B,MAAM,IAAIH,oBAAY,CAAC,WAAW,CAAC,0CAA0C,CAAC;IAChF;IAEA,MAAMI,kBAAkB,CAAC,uBAAuB,CAAC;IACjD,MAAMC,gBAAgB,CAAC,sDAAsD,EAAED,iBAAiB;IAChG,MAAME,UAAUC,IAAAA,QAAG,EAAC,CAAC,QAAQ,EAAEH,iBAAiB,EAAEI,KAAK;IACvD,IAAI;QACF,MAAMC,SAAS,MAAMC,IAAAA,sBAAgB,EAACN;QAEtC,IAAIK,QAAQ;YACVH,QAAQK,OAAO,CAAC,CAAC,OAAO,EAAEP,iBAAiB;QAC7C,OAAO;YACLE,QAAQM,IAAI,CAACP;QACf;IACF,EAAE,OAAOQ,OAAO;QACdP,QAAQM,IAAI,CAACP;QACb,MAAMQ;IACR;AACF"}