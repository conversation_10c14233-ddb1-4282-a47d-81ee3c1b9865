{"version": 3, "sources": ["../../../src/utils/obj.ts"], "sourcesContent": ["/** `lodash.get` */\nexport function get(obj: any, key: string): any | null {\n  const branches = key.split('.');\n  let current: any = obj;\n  let branch: string | undefined;\n  while ((branch = branches.shift())) {\n    if (!(branch in current)) {\n      return null;\n    }\n    current = current[branch];\n  }\n  return current;\n}\n\n/** `lodash.set` */\nexport function set(obj: any, key: string, value: any): any | null {\n  const branches = key.split('.');\n  let current: any = obj;\n  let branch: string | undefined;\n  while ((branch = branches.shift())) {\n    if (branches.length === 0) {\n      current[branch] = value;\n      return obj;\n    }\n\n    if (!(branch in current)) {\n      current[branch] = {};\n    }\n\n    current = current[branch];\n  }\n  return null;\n}\n\n/** `lodash.pickBy` */\nexport function pickBy<T>(\n  obj: { [key: string]: T },\n  predicate: (value: T, key: string) => boolean | undefined\n) {\n  return Object.entries(obj).reduce(\n    (acc, [key, value]) => {\n      if (predicate(value, key)) {\n        acc[key] = value;\n      }\n      return acc;\n    },\n    {} as { [key: string]: T }\n  );\n}\n"], "names": ["get", "pickBy", "set", "obj", "key", "branches", "split", "current", "branch", "shift", "value", "length", "predicate", "Object", "entries", "reduce", "acc"], "mappings": "AAAA,iBAAiB;;;;;;;;;;;IACDA,GAAG;eAAHA;;IAkCAC,MAAM;eAANA;;IApBAC,GAAG;eAAHA;;;AAdT,SAASF,IAAIG,GAAQ,EAAEC,GAAW;IACvC,MAAMC,WAAWD,IAAIE,KAAK,CAAC;IAC3B,IAAIC,UAAeJ;IACnB,IAAIK;IACJ,MAAQA,SAASH,SAASI,KAAK,GAAK;QAClC,IAAI,CAAED,CAAAA,UAAUD,OAAM,GAAI;YACxB,OAAO;QACT;QACAA,UAAUA,OAAO,CAACC,OAAO;IAC3B;IACA,OAAOD;AACT;AAGO,SAASL,IAAIC,GAAQ,EAAEC,GAAW,EAAEM,KAAU;IACnD,MAAML,WAAWD,IAAIE,KAAK,CAAC;IAC3B,IAAIC,UAAeJ;IACnB,IAAIK;IACJ,MAAQA,SAASH,SAASI,KAAK,GAAK;QAClC,IAAIJ,SAASM,MAAM,KAAK,GAAG;YACzBJ,OAAO,CAACC,OAAO,GAAGE;YAClB,OAAOP;QACT;QAEA,IAAI,CAAEK,CAAAA,UAAUD,OAAM,GAAI;YACxBA,OAAO,CAACC,OAAO,GAAG,CAAC;QACrB;QAEAD,UAAUA,OAAO,CAACC,OAAO;IAC3B;IACA,OAAO;AACT;AAGO,SAASP,OACdE,GAAyB,EACzBS,SAAyD;IAEzD,OAAOC,OAAOC,OAAO,CAACX,KAAKY,MAAM,CAC/B,CAACC,KAAK,CAACZ,KAAKM,MAAM;QAChB,IAAIE,UAAUF,OAAON,MAAM;YACzBY,GAAG,CAACZ,IAAI,GAAGM;QACb;QACA,OAAOM;IACT,GACA,CAAC;AAEL"}