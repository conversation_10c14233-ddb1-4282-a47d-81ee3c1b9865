{"version": 3, "sources": ["../../../src/utils/downloadAppAsync.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\nimport { Readable, Stream } from 'stream';\nimport { Agent } from 'undici';\nimport { promisify } from 'util';\n\nimport { createTempFilePath } from './createTempPath';\nimport { ensureDirectoryAsync } from './dir';\nimport { CommandError } from './errors';\nimport { extractAsync } from './tar';\nimport { createCachedFetch, fetchAsync } from '../api/rest/client';\nimport { FetchLike, ProgressCallback } from '../api/rest/client.types';\n\nconst debug = require('debug')('expo:utils:downloadAppAsync') as typeof console.log;\n\nconst TIMER_DURATION = 30000;\n\nconst pipeline = promisify(Stream.pipeline);\n\nasync function downloadAsync({\n  url,\n  outputPath,\n  cacheDirectory,\n  onProgress,\n}: {\n  url: string;\n  outputPath: string;\n  cacheDirectory?: string;\n  onProgress?: ProgressCallback;\n}) {\n  let fetchInstance: FetchLike = fetchAsync;\n  if (cacheDirectory) {\n    // Reconstruct the cached fetch since caching could be disabled.\n    fetchInstance = createCachedFetch({\n      // We'll use a 1 week cache for versions so older values get flushed out eventually.\n      ttl: 1000 * 60 * 60 * 24 * 7,\n      // Users can also nuke their `~/.expo` directory to clear the cache.\n      cacheDirectory,\n    });\n  }\n\n  debug(`Downloading ${url} to ${outputPath}`);\n  const res = await fetchInstance(url, {\n    onProgress,\n    dispatcher: new Agent({ connectTimeout: TIMER_DURATION }),\n  });\n  if (!res.ok || !res.body) {\n    throw new CommandError(\n      'FILE_DOWNLOAD',\n      `Unexpected response: ${res.statusText}. From url: ${url}`\n    );\n  }\n  return pipeline(Readable.fromWeb(res.body), fs.createWriteStream(outputPath));\n}\n\nexport async function downloadAppAsync({\n  url,\n  outputPath,\n  extract = false,\n  cacheDirectory,\n  onProgress,\n}: {\n  url: string;\n  outputPath: string;\n  extract?: boolean;\n  cacheDirectory?: string;\n  onProgress?: ProgressCallback;\n}): Promise<void> {\n  if (extract) {\n    // For iOS we download the ipa to a file then pass that file into the extractor.\n    // In the future we should just pipe the `res.body -> tar.extract` directly.\n    // I tried this and it created some weird errors where observing the data stream\n    // would corrupt the file causing tar to fail with `TAR_BAD_ARCHIVE`.\n    const tmpPath = createTempFilePath(path.basename(outputPath));\n    await downloadAsync({ url, outputPath: tmpPath, cacheDirectory, onProgress });\n    debug(`Extracting ${tmpPath} to ${outputPath}`);\n    await ensureDirectoryAsync(outputPath);\n    await extractAsync(tmpPath, outputPath);\n  } else {\n    await ensureDirectoryAsync(path.dirname(outputPath));\n    await downloadAsync({ url, outputPath, cacheDirectory, onProgress });\n  }\n}\n"], "names": ["downloadAppAsync", "debug", "require", "TIMER_DURATION", "pipeline", "promisify", "Stream", "downloadAsync", "url", "outputPath", "cacheDirectory", "onProgress", "fetchInstance", "fetchAsync", "createCachedFetch", "ttl", "res", "dispatcher", "Agent", "connectTimeout", "ok", "body", "CommandError", "statusText", "Readable", "fromWeb", "fs", "createWriteStream", "extract", "tmpPath", "createTempFilePath", "path", "basename", "ensureDirectoryAsync", "extractAsync", "dirname"], "mappings": ";;;;+BAuDsBA;;;eAAAA;;;;gEAvDP;;;;;;;gEACE;;;;;;;yBACgB;;;;;;;yBACX;;;;;;;yBACI;;;;;;gCAES;qBACE;wBACR;qBACA;wBACiB;;;;;;AAG9C,MAAMC,QAAQC,QAAQ,SAAS;AAE/B,MAAMC,iBAAiB;AAEvB,MAAMC,WAAWC,IAAAA,iBAAS,EAACC,gBAAM,CAACF,QAAQ;AAE1C,eAAeG,cAAc,EAC3BC,GAAG,EACHC,UAAU,EACVC,cAAc,EACdC,UAAU,EAMX;IACC,IAAIC,gBAA2BC,kBAAU;IACzC,IAAIH,gBAAgB;QAClB,gEAAgE;QAChEE,gBAAgBE,IAAAA,yBAAiB,EAAC;YAChC,oFAAoF;YACpFC,KAAK,OAAO,KAAK,KAAK,KAAK;YAC3B,oEAAoE;YACpEL;QACF;IACF;IAEAT,MAAM,CAAC,YAAY,EAAEO,IAAI,IAAI,EAAEC,YAAY;IAC3C,MAAMO,MAAM,MAAMJ,cAAcJ,KAAK;QACnCG;QACAM,YAAY,IAAIC,CAAAA,SAAI,OAAC,CAAC;YAAEC,gBAAgBhB;QAAe;IACzD;IACA,IAAI,CAACa,IAAII,EAAE,IAAI,CAACJ,IAAIK,IAAI,EAAE;QACxB,MAAM,IAAIC,oBAAY,CACpB,iBACA,CAAC,qBAAqB,EAAEN,IAAIO,UAAU,CAAC,YAAY,EAAEf,KAAK;IAE9D;IACA,OAAOJ,SAASoB,kBAAQ,CAACC,OAAO,CAACT,IAAIK,IAAI,GAAGK,aAAE,CAACC,iBAAiB,CAAClB;AACnE;AAEO,eAAeT,iBAAiB,EACrCQ,GAAG,EACHC,UAAU,EACVmB,UAAU,KAAK,EACflB,cAAc,EACdC,UAAU,EAOX;IACC,IAAIiB,SAAS;QACX,gFAAgF;QAChF,4EAA4E;QAC5E,gFAAgF;QAChF,qEAAqE;QACrE,MAAMC,UAAUC,IAAAA,kCAAkB,EAACC,eAAI,CAACC,QAAQ,CAACvB;QACjD,MAAMF,cAAc;YAAEC;YAAKC,YAAYoB;YAASnB;YAAgBC;QAAW;QAC3EV,MAAM,CAAC,WAAW,EAAE4B,QAAQ,IAAI,EAAEpB,YAAY;QAC9C,MAAMwB,IAAAA,yBAAoB,EAACxB;QAC3B,MAAMyB,IAAAA,iBAAY,EAACL,SAASpB;IAC9B,OAAO;QACL,MAAMwB,IAAAA,yBAAoB,EAACF,eAAI,CAACI,OAAO,CAAC1B;QACxC,MAAMF,cAAc;YAAEC;YAAKC;YAAYC;YAAgBC;QAAW;IACpE;AACF"}