{"version": 3, "sources": ["../../../src/utils/mergeGitIgnorePaths.ts"], "sourcesContent": ["import crypto from 'crypto';\nimport fs from 'fs';\n\nimport { Log } from '../log';\n\ntype MergeResults = {\n  contents: string;\n  didClear: boolean;\n  didMerge: boolean;\n};\n\nconst generatedHeaderPrefix = `# @generated expo-cli`;\nexport const generatedFooterComment = `# @end expo-cli`;\n\n/**\n * Merge two gitignore files together and add a generated header.\n *\n * @param targetGitIgnorePath\n * @param sourceGitIgnorePath\n *\n * @returns `null` if one of the gitignore files doesn't exist. Otherwise, returns the merged contents.\n */\nexport function mergeGitIgnorePaths(\n  targetGitIgnorePath: string,\n  sourceGitIgnorePath: string\n): null | MergeResults {\n  if (!fs.existsSync(targetGitIgnorePath)) {\n    // No gitignore in the project already, no need to merge anything into anything. I guess they\n    // are not using git :O\n    return null;\n  }\n\n  if (!fs.existsSync(sourceGitIgnorePath)) {\n    // Maybe we don't have a gitignore in the template project\n    return null;\n  }\n\n  const targetGitIgnore = fs.readFileSync(targetGitIgnorePath).toString();\n  const sourceGitIgnore = fs.readFileSync(sourceGitIgnorePath).toString();\n  const merged = mergeGitIgnoreContents(targetGitIgnore, sourceGitIgnore);\n  // Only rewrite the file if it was modified.\n  if (merged.contents) {\n    fs.writeFileSync(targetGitIgnorePath, merged.contents);\n  }\n\n  return merged;\n}\n\n/**\n * Get line indexes for the generated section of a gitignore.\n *\n * @param gitIgnore\n */\nfunction getGeneratedSectionIndexes(gitIgnore: string): {\n  contents: string[];\n  start: number;\n  end: number;\n} {\n  const contents = gitIgnore.split('\\n');\n  const start = contents.findIndex((line) => line.startsWith(generatedHeaderPrefix));\n  const end = contents.findIndex((line) => line.startsWith(generatedFooterComment));\n\n  return { contents, start, end };\n}\n\n/**\n * Removes the generated section from a gitignore, returns null when nothing can be removed.\n * This sways heavily towards not removing lines unless it's certain that modifications were not made to the gitignore manually.\n *\n * @param gitIgnore\n */\nexport function removeGeneratedGitIgnoreContents(gitIgnore: string): string | null {\n  const { contents, start, end } = getGeneratedSectionIndexes(gitIgnore);\n  if (start > -1 && end > -1 && start < end) {\n    contents.splice(start, end - start + 1);\n    // TODO: We could in theory check that the contents we're removing match the hash used in the header,\n    // this would ensure that we don't accidentally remove lines that someone added or removed from the generated section.\n    return contents.join('\\n');\n  }\n  return null;\n}\n\n/**\n * Merge the contents of two gitignores together and add a generated header.\n *\n * @param targetGitIgnore contents of the existing gitignore\n * @param sourceGitIgnore contents of the extra gitignore\n */\nexport function mergeGitIgnoreContents(\n  targetGitIgnore: string,\n  sourceGitIgnore: string\n): MergeResults {\n  const header = createGeneratedHeaderComment(sourceGitIgnore);\n  if (!targetGitIgnore.includes(header)) {\n    // Ensure the old generated gitignore contents are removed.\n    const sanitizedTarget = removeGeneratedGitIgnoreContents(targetGitIgnore);\n    return {\n      contents: [\n        sanitizedTarget ?? targetGitIgnore,\n        header,\n        `# The following patterns were generated by expo-cli`,\n        ``,\n        sourceGitIgnore,\n        generatedFooterComment,\n      ].join('\\n'),\n      didMerge: true,\n      didClear: !!sanitizedTarget,\n    };\n  }\n  return { contents: targetGitIgnore, didClear: false, didMerge: false };\n}\n\n/**\n * Adds the contents into an existing gitignore \"generated by expo-cli section\"\n * If no section exists, it will be created (hence the name upsert)\n */\nexport function upsertGitIgnoreContents(\n  targetGitIgnorePath: string,\n  contents: string\n): MergeResults | null {\n  const targetGitIgnore = fs.readFileSync(targetGitIgnorePath, {\n    encoding: 'utf-8',\n    flag: 'a+',\n  });\n\n  if (targetGitIgnore.match(new RegExp(`^${contents}[\\\\n\\\\r\\\\s]*$`, 'm'))) {\n    return null;\n  }\n\n  // If there is an existing section, update it with the new content\n  if (targetGitIgnore.includes(generatedHeaderPrefix)) {\n    const indexes = getGeneratedSectionIndexes(targetGitIgnore);\n\n    contents = `${indexes.contents.slice(indexes.start + 3, indexes.end).join('\\n')}\\n${contents}`;\n  }\n\n  const merged = mergeGitIgnoreContents(targetGitIgnore, contents);\n\n  if (merged.contents) {\n    fs.writeFileSync(targetGitIgnorePath, merged.contents);\n  }\n  return merged;\n}\n\nexport function createGeneratedHeaderComment(gitIgnore: string): string {\n  const hashKey = createGitIgnoreHash(getSanitizedGitIgnoreLines(gitIgnore).join('\\n'));\n\n  return `${generatedHeaderPrefix} ${hashKey}`;\n}\n\n/**\n * Normalize the contents of a gitignore to ensure that minor changes like new lines or sort order don't cause a regeneration.\n */\nexport function getSanitizedGitIgnoreLines(gitIgnore: string): string[] {\n  // filter, trim, and sort the lines.\n  return gitIgnore\n    .split('\\n')\n    .filter((v) => {\n      const line = v.trim();\n      // Strip comments\n      if (line.startsWith('#')) {\n        return false;\n      }\n      return !!line;\n    })\n    .sort();\n}\n\nexport function createGitIgnoreHash(gitIgnore: string): string {\n  // this doesn't need to be secure, the shorter the better.\n  const hash = crypto.createHash('sha1').update(gitIgnore).digest('hex');\n  return `sync-${hash}`;\n}\n\nexport function removeFromGitIgnore(targetGitIgnorePath: string, contents: string) {\n  try {\n    if (!fs.existsSync(targetGitIgnorePath)) {\n      return;\n    }\n\n    let targetGitIgnore = fs.readFileSync(targetGitIgnorePath, 'utf-8');\n\n    if (!targetGitIgnore.includes(contents)) {\n      return null;\n    }\n\n    targetGitIgnore = targetGitIgnore.replace(`${contents}\\n`, '');\n\n    const indexes = getGeneratedSectionIndexes(targetGitIgnore);\n\n    if (indexes.start === indexes.end - 3) {\n      targetGitIgnore = targetGitIgnore.replace(\n        new RegExp(`^${generatedHeaderPrefix}((.|\\n)*)${generatedFooterComment}$`, 'm'),\n        ''\n      );\n    }\n\n    return fs.writeFileSync(targetGitIgnorePath, targetGitIgnore);\n  } catch (error) {\n    Log.error(`Failed to read/write to .gitignore path: ${targetGitIgnorePath}`);\n    throw error;\n  }\n}\n"], "names": ["createGeneratedHeaderComment", "createGitIgnoreHash", "generatedFooterComment", "getSanitizedGitIgnoreLines", "mergeGitIgnoreContents", "mergeGitIgnorePaths", "removeFromGitIgnore", "removeGeneratedGitIgnoreContents", "upsertGitIgnoreContents", "generatedHeaderPrefix", "targetGitIgnorePath", "sourceGitIgnorePath", "fs", "existsSync", "targetGitIgnore", "readFileSync", "toString", "sourceGitIgnore", "merged", "contents", "writeFileSync", "getGeneratedSectionIndexes", "gitIgnore", "split", "start", "findIndex", "line", "startsWith", "end", "splice", "join", "header", "includes", "sanitizedTarget", "didMerge", "<PERSON><PERSON><PERSON><PERSON>", "encoding", "flag", "match", "RegExp", "indexes", "slice", "hash<PERSON><PERSON>", "filter", "v", "trim", "sort", "hash", "crypto", "createHash", "update", "digest", "replace", "error", "Log"], "mappings": ";;;;;;;;;;;IAgJgBA,4BAA4B;eAA5BA;;IAwBAC,mBAAmB;eAAnBA;;IA5JHC,sBAAsB;eAAtBA;;IA6IGC,0BAA0B;eAA1BA;;IAjEAC,sBAAsB;eAAtBA;;IAlEAC,mBAAmB;eAAnBA;;IAwJAC,mBAAmB;eAAnBA;;IAvGAC,gCAAgC;eAAhCA;;IA6CAC,uBAAuB;eAAvBA;;;;gEApHG;;;;;;;gEACJ;;;;;;qBAEK;;;;;;AAQpB,MAAMC,wBAAwB,CAAC,qBAAqB,CAAC;AAC9C,MAAMP,yBAAyB,CAAC,eAAe,CAAC;AAUhD,SAASG,oBACdK,mBAA2B,EAC3BC,mBAA2B;IAE3B,IAAI,CAACC,aAAE,CAACC,UAAU,CAACH,sBAAsB;QACvC,6FAA6F;QAC7F,uBAAuB;QACvB,OAAO;IACT;IAEA,IAAI,CAACE,aAAE,CAACC,UAAU,CAACF,sBAAsB;QACvC,0DAA0D;QAC1D,OAAO;IACT;IAEA,MAAMG,kBAAkBF,aAAE,CAACG,YAAY,CAACL,qBAAqBM,QAAQ;IACrE,MAAMC,kBAAkBL,aAAE,CAACG,YAAY,CAACJ,qBAAqBK,QAAQ;IACrE,MAAME,SAASd,uBAAuBU,iBAAiBG;IACvD,4CAA4C;IAC5C,IAAIC,OAAOC,QAAQ,EAAE;QACnBP,aAAE,CAACQ,aAAa,CAACV,qBAAqBQ,OAAOC,QAAQ;IACvD;IAEA,OAAOD;AACT;AAEA;;;;CAIC,GACD,SAASG,2BAA2BC,SAAiB;IAKnD,MAAMH,WAAWG,UAAUC,KAAK,CAAC;IACjC,MAAMC,QAAQL,SAASM,SAAS,CAAC,CAACC,OAASA,KAAKC,UAAU,CAAClB;IAC3D,MAAMmB,MAAMT,SAASM,SAAS,CAAC,CAACC,OAASA,KAAKC,UAAU,CAACzB;IAEzD,OAAO;QAAEiB;QAAUK;QAAOI;IAAI;AAChC;AAQO,SAASrB,iCAAiCe,SAAiB;IAChE,MAAM,EAAEH,QAAQ,EAAEK,KAAK,EAAEI,GAAG,EAAE,GAAGP,2BAA2BC;IAC5D,IAAIE,QAAQ,CAAC,KAAKI,MAAM,CAAC,KAAKJ,QAAQI,KAAK;QACzCT,SAASU,MAAM,CAACL,OAAOI,MAAMJ,QAAQ;QACrC,qGAAqG;QACrG,sHAAsH;QACtH,OAAOL,SAASW,IAAI,CAAC;IACvB;IACA,OAAO;AACT;AAQO,SAAS1B,uBACdU,eAAuB,EACvBG,eAAuB;IAEvB,MAAMc,SAAS/B,6BAA6BiB;IAC5C,IAAI,CAACH,gBAAgBkB,QAAQ,CAACD,SAAS;QACrC,2DAA2D;QAC3D,MAAME,kBAAkB1B,iCAAiCO;QACzD,OAAO;YACLK,UAAU;gBACRc,mBAAmBnB;gBACnBiB;gBACA,CAAC,mDAAmD,CAAC;gBACrD,EAAE;gBACFd;gBACAf;aACD,CAAC4B,IAAI,CAAC;YACPI,UAAU;YACVC,UAAU,CAAC,CAACF;QACd;IACF;IACA,OAAO;QAAEd,UAAUL;QAAiBqB,UAAU;QAAOD,UAAU;IAAM;AACvE;AAMO,SAAS1B,wBACdE,mBAA2B,EAC3BS,QAAgB;IAEhB,MAAML,kBAAkBF,aAAE,CAACG,YAAY,CAACL,qBAAqB;QAC3D0B,UAAU;QACVC,MAAM;IACR;IAEA,IAAIvB,gBAAgBwB,KAAK,CAAC,IAAIC,OAAO,CAAC,CAAC,EAAEpB,SAAS,aAAa,CAAC,EAAE,OAAO;QACvE,OAAO;IACT;IAEA,kEAAkE;IAClE,IAAIL,gBAAgBkB,QAAQ,CAACvB,wBAAwB;QACnD,MAAM+B,UAAUnB,2BAA2BP;QAE3CK,WAAW,GAAGqB,QAAQrB,QAAQ,CAACsB,KAAK,CAACD,QAAQhB,KAAK,GAAG,GAAGgB,QAAQZ,GAAG,EAAEE,IAAI,CAAC,MAAM,EAAE,EAAEX,UAAU;IAChG;IAEA,MAAMD,SAASd,uBAAuBU,iBAAiBK;IAEvD,IAAID,OAAOC,QAAQ,EAAE;QACnBP,aAAE,CAACQ,aAAa,CAACV,qBAAqBQ,OAAOC,QAAQ;IACvD;IACA,OAAOD;AACT;AAEO,SAASlB,6BAA6BsB,SAAiB;IAC5D,MAAMoB,UAAUzC,oBAAoBE,2BAA2BmB,WAAWQ,IAAI,CAAC;IAE/E,OAAO,GAAGrB,sBAAsB,CAAC,EAAEiC,SAAS;AAC9C;AAKO,SAASvC,2BAA2BmB,SAAiB;IAC1D,oCAAoC;IACpC,OAAOA,UACJC,KAAK,CAAC,MACNoB,MAAM,CAAC,CAACC;QACP,MAAMlB,OAAOkB,EAAEC,IAAI;QACnB,iBAAiB;QACjB,IAAInB,KAAKC,UAAU,CAAC,MAAM;YACxB,OAAO;QACT;QACA,OAAO,CAAC,CAACD;IACX,GACCoB,IAAI;AACT;AAEO,SAAS7C,oBAAoBqB,SAAiB;IACnD,0DAA0D;IAC1D,MAAMyB,OAAOC,iBAAM,CAACC,UAAU,CAAC,QAAQC,MAAM,CAAC5B,WAAW6B,MAAM,CAAC;IAChE,OAAO,CAAC,KAAK,EAAEJ,MAAM;AACvB;AAEO,SAASzC,oBAAoBI,mBAA2B,EAAES,QAAgB;IAC/E,IAAI;QACF,IAAI,CAACP,aAAE,CAACC,UAAU,CAACH,sBAAsB;YACvC;QACF;QAEA,IAAII,kBAAkBF,aAAE,CAACG,YAAY,CAACL,qBAAqB;QAE3D,IAAI,CAACI,gBAAgBkB,QAAQ,CAACb,WAAW;YACvC,OAAO;QACT;QAEAL,kBAAkBA,gBAAgBsC,OAAO,CAAC,GAAGjC,SAAS,EAAE,CAAC,EAAE;QAE3D,MAAMqB,UAAUnB,2BAA2BP;QAE3C,IAAI0B,QAAQhB,KAAK,KAAKgB,QAAQZ,GAAG,GAAG,GAAG;YACrCd,kBAAkBA,gBAAgBsC,OAAO,CACvC,IAAIb,OAAO,CAAC,CAAC,EAAE9B,sBAAsB,SAAS,EAAEP,uBAAuB,CAAC,CAAC,EAAE,MAC3E;QAEJ;QAEA,OAAOU,aAAE,CAACQ,aAAa,CAACV,qBAAqBI;IAC/C,EAAE,OAAOuC,OAAO;QACdC,QAAG,CAACD,KAAK,CAAC,CAAC,yCAAyC,EAAE3C,qBAAqB;QAC3E,MAAM2C;IACR;AACF"}