{"version": 3, "sources": ["../../../src/utils/port.ts"], "sourcesContent": ["import chalk from 'chalk';\nimport freeportAsync from 'freeport-async';\n\nimport { env } from './env';\nimport { CommandError } from './errors';\nimport * as Log from '../log';\n\n/** Get a free port or assert a CLI command error. */\nexport async function getFreePortAsync(rangeStart: number): Promise<number> {\n  const port = await freeportAsync(rangeStart, { hostnames: [null, 'localhost'] });\n  if (!port) {\n    throw new CommandError('NO_PORT_FOUND', 'No available port found');\n  }\n\n  return port;\n}\n\n/** @return `true` if the port can still be used to start the dev server, `false` if the dev server should be skipped, and asserts if the port is now taken. */\nexport async function ensurePortAvailabilityAsync(\n  projectRoot: string,\n  { port }: { port: number }\n): Promise<boolean> {\n  const freePort = await freeportAsync(port, { hostnames: [null] });\n  // Check if port has become busy during the build.\n  if (freePort === port) {\n    return true;\n  }\n\n  const isBusy = await isBusyPortRunningSameProcessAsync(projectRoot, { port });\n  if (!isBusy) {\n    throw new CommandError(\n      `Port \"${port}\" became busy running another process while the app was compiling. Re-run command to use a new port.`\n    );\n  }\n\n  // Log that the dev server will not be started and that the logs will appear in another window.\n  Log.log(\n    '› The dev server for this app is already running in another window. Logs will appear there.'\n  );\n  return false;\n}\n\nfunction isRestrictedPort(port: number) {\n  if (process.platform !== 'win32' && port < 1024) {\n    const isRoot = process.getuid && process.getuid() === 0;\n    return !isRoot;\n  }\n  return false;\n}\n\nasync function isBusyPortRunningSameProcessAsync(projectRoot: string, { port }: { port: number }) {\n  const { getRunningProcess } =\n    require('./getRunningProcess') as typeof import('./getRunningProcess');\n\n  const runningProcess = isRestrictedPort(port) ? null : getRunningProcess(port);\n  if (runningProcess) {\n    if (runningProcess.directory === projectRoot) {\n      return true;\n    } else {\n      return false;\n    }\n  }\n\n  return null;\n}\n\n// TODO(Bacon): Revisit after all start and run code is merged.\nexport async function choosePortAsync(\n  projectRoot: string,\n  {\n    defaultPort,\n    host,\n    reuseExistingPort,\n  }: {\n    defaultPort: number;\n    host?: string;\n    reuseExistingPort?: boolean;\n  }\n): Promise<number | null> {\n  try {\n    const port = await freeportAsync(defaultPort, { hostnames: [host ?? null] });\n    if (port === defaultPort || defaultPort === 0) {\n      return port;\n    }\n\n    const isRestricted = isRestrictedPort(port);\n\n    let message = isRestricted\n      ? `Admin permissions are required to run a server on a port below 1024`\n      : `Port ${chalk.bold(defaultPort)} is`;\n\n    const { getRunningProcess } =\n      require('./getRunningProcess') as typeof import('./getRunningProcess');\n    const runningProcess = isRestricted ? null : getRunningProcess(defaultPort);\n\n    if (runningProcess) {\n      const pidTag = chalk.gray(`(pid ${runningProcess.pid})`);\n      if (runningProcess.directory === projectRoot) {\n        message += ` running this app in another window`;\n        if (reuseExistingPort) {\n          return null;\n        }\n      } else {\n        message += ` running ${chalk.cyan(runningProcess.command)} in another window`;\n      }\n      message += '\\n' + chalk.gray(`  ${runningProcess.directory} ${pidTag}`);\n    } else {\n      message += ' being used by another process';\n    }\n\n    Log.log(`\\u203A ${message}`);\n    const { confirmAsync } = require('./prompts') as typeof import('./prompts');\n    const change = await confirmAsync({\n      message: `Use port ${port} instead?`,\n      initial: true,\n    });\n    return change ? port : null;\n  } catch (error: any) {\n    if (error.code === 'ABORTED') {\n      throw error;\n    } else if (error.code === 'NON_INTERACTIVE') {\n      Log.warn(chalk.yellow(error.message));\n      return null;\n    }\n    throw error;\n  }\n}\n\n// TODO(Bacon): Revisit after all start and run code is merged.\nexport async function resolvePortAsync(\n  projectRoot: string,\n  {\n    /** Should opt to reuse a port that is running the same project in another window. */\n    reuseExistingPort,\n    /** Preferred port. */\n    defaultPort,\n    /** Backup port for when the default isn't available. */\n    fallbackPort,\n  }: {\n    reuseExistingPort?: boolean;\n    defaultPort?: string | number;\n    fallbackPort?: number;\n  } = {}\n): Promise<number | null> {\n  let port: number;\n  if (typeof defaultPort === 'string') {\n    port = parseInt(defaultPort, 10);\n  } else if (typeof defaultPort === 'number') {\n    port = defaultPort;\n  } else {\n    port = env.RCT_METRO_PORT || fallbackPort || 8081;\n  }\n\n  // Only check the port when the bundler is running.\n  const resolvedPort = await choosePortAsync(projectRoot, {\n    defaultPort: port,\n    reuseExistingPort,\n  });\n  if (resolvedPort == null) {\n    Log.log('\\u203A Skipping dev server');\n    // Skip bundling if the port is null\n  } else {\n    // Use the new or resolved port\n    process.env.RCT_METRO_PORT = String(resolvedPort);\n  }\n\n  return resolvedPort;\n}\n"], "names": ["choosePortAsync", "ensurePortAvailabilityAsync", "getFreePortAsync", "resolvePortAsync", "rangeStart", "port", "freeportAsync", "hostnames", "CommandError", "projectRoot", "freePort", "isBusy", "isBusyPortRunningSameProcessAsync", "Log", "log", "isRestrictedPort", "process", "platform", "isRoot", "getuid", "getRunningProcess", "require", "runningProcess", "directory", "defaultPort", "host", "reuseExistingPort", "isRestricted", "message", "chalk", "bold", "pidTag", "gray", "pid", "cyan", "command", "<PERSON><PERSON><PERSON>", "change", "initial", "error", "code", "warn", "yellow", "fallback<PERSON>ort", "parseInt", "env", "RCT_METRO_PORT", "resolvedPort", "String"], "mappings": ";;;;;;;;;;;IAmEsBA,eAAe;eAAfA;;IAjDAC,2BAA2B;eAA3BA;;IAVAC,gBAAgB;eAAhBA;;IAyHAC,gBAAgB;eAAhBA;;;;gEAjIJ;;;;;;;gEACQ;;;;;;qBAEN;wBACS;6DACR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGd,eAAeD,iBAAiBE,UAAkB;IACvD,MAAMC,OAAO,MAAMC,IAAAA,wBAAa,EAACF,YAAY;QAAEG,WAAW;YAAC;YAAM;SAAY;IAAC;IAC9E,IAAI,CAACF,MAAM;QACT,MAAM,IAAIG,oBAAY,CAAC,iBAAiB;IAC1C;IAEA,OAAOH;AACT;AAGO,eAAeJ,4BACpBQ,WAAmB,EACnB,EAAEJ,IAAI,EAAoB;IAE1B,MAAMK,WAAW,MAAMJ,IAAAA,wBAAa,EAACD,MAAM;QAAEE,WAAW;YAAC;SAAK;IAAC;IAC/D,kDAAkD;IAClD,IAAIG,aAAaL,MAAM;QACrB,OAAO;IACT;IAEA,MAAMM,SAAS,MAAMC,kCAAkCH,aAAa;QAAEJ;IAAK;IAC3E,IAAI,CAACM,QAAQ;QACX,MAAM,IAAIH,oBAAY,CACpB,CAAC,MAAM,EAAEH,KAAK,oGAAoG,CAAC;IAEvH;IAEA,+FAA+F;IAC/FQ,KAAIC,GAAG,CACL;IAEF,OAAO;AACT;AAEA,SAASC,iBAAiBV,IAAY;IACpC,IAAIW,QAAQC,QAAQ,KAAK,WAAWZ,OAAO,MAAM;QAC/C,MAAMa,SAASF,QAAQG,MAAM,IAAIH,QAAQG,MAAM,OAAO;QACtD,OAAO,CAACD;IACV;IACA,OAAO;AACT;AAEA,eAAeN,kCAAkCH,WAAmB,EAAE,EAAEJ,IAAI,EAAoB;IAC9F,MAAM,EAAEe,iBAAiB,EAAE,GACzBC,QAAQ;IAEV,MAAMC,iBAAiBP,iBAAiBV,QAAQ,OAAOe,kBAAkBf;IACzE,IAAIiB,gBAAgB;QAClB,IAAIA,eAAeC,SAAS,KAAKd,aAAa;YAC5C,OAAO;QACT,OAAO;YACL,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAGO,eAAeT,gBACpBS,WAAmB,EACnB,EACEe,WAAW,EACXC,IAAI,EACJC,iBAAiB,EAKlB;IAED,IAAI;QACF,MAAMrB,OAAO,MAAMC,IAAAA,wBAAa,EAACkB,aAAa;YAAEjB,WAAW;gBAACkB,QAAQ;aAAK;QAAC;QAC1E,IAAIpB,SAASmB,eAAeA,gBAAgB,GAAG;YAC7C,OAAOnB;QACT;QAEA,MAAMsB,eAAeZ,iBAAiBV;QAEtC,IAAIuB,UAAUD,eACV,CAAC,mEAAmE,CAAC,GACrE,CAAC,KAAK,EAAEE,gBAAK,CAACC,IAAI,CAACN,aAAa,GAAG,CAAC;QAExC,MAAM,EAAEJ,iBAAiB,EAAE,GACzBC,QAAQ;QACV,MAAMC,iBAAiBK,eAAe,OAAOP,kBAAkBI;QAE/D,IAAIF,gBAAgB;YAClB,MAAMS,SAASF,gBAAK,CAACG,IAAI,CAAC,CAAC,KAAK,EAAEV,eAAeW,GAAG,CAAC,CAAC,CAAC;YACvD,IAAIX,eAAeC,SAAS,KAAKd,aAAa;gBAC5CmB,WAAW,CAAC,mCAAmC,CAAC;gBAChD,IAAIF,mBAAmB;oBACrB,OAAO;gBACT;YACF,OAAO;gBACLE,WAAW,CAAC,SAAS,EAAEC,gBAAK,CAACK,IAAI,CAACZ,eAAea,OAAO,EAAE,kBAAkB,CAAC;YAC/E;YACAP,WAAW,OAAOC,gBAAK,CAACG,IAAI,CAAC,CAAC,EAAE,EAAEV,eAAeC,SAAS,CAAC,CAAC,EAAEQ,QAAQ;QACxE,OAAO;YACLH,WAAW;QACb;QAEAf,KAAIC,GAAG,CAAC,CAAC,OAAO,EAAEc,SAAS;QAC3B,MAAM,EAAEQ,YAAY,EAAE,GAAGf,QAAQ;QACjC,MAAMgB,SAAS,MAAMD,aAAa;YAChCR,SAAS,CAAC,SAAS,EAAEvB,KAAK,SAAS,CAAC;YACpCiC,SAAS;QACX;QACA,OAAOD,SAAShC,OAAO;IACzB,EAAE,OAAOkC,OAAY;QACnB,IAAIA,MAAMC,IAAI,KAAK,WAAW;YAC5B,MAAMD;QACR,OAAO,IAAIA,MAAMC,IAAI,KAAK,mBAAmB;YAC3C3B,KAAI4B,IAAI,CAACZ,gBAAK,CAACa,MAAM,CAACH,MAAMX,OAAO;YACnC,OAAO;QACT;QACA,MAAMW;IACR;AACF;AAGO,eAAepC,iBACpBM,WAAmB,EACnB,EACE,mFAAmF,GACnFiB,iBAAiB,EACjB,oBAAoB,GACpBF,WAAW,EACX,sDAAsD,GACtDmB,YAAY,EAKb,GAAG,CAAC,CAAC;IAEN,IAAItC;IACJ,IAAI,OAAOmB,gBAAgB,UAAU;QACnCnB,OAAOuC,SAASpB,aAAa;IAC/B,OAAO,IAAI,OAAOA,gBAAgB,UAAU;QAC1CnB,OAAOmB;IACT,OAAO;QACLnB,OAAOwC,QAAG,CAACC,cAAc,IAAIH,gBAAgB;IAC/C;IAEA,mDAAmD;IACnD,MAAMI,eAAe,MAAM/C,gBAAgBS,aAAa;QACtDe,aAAanB;QACbqB;IACF;IACA,IAAIqB,gBAAgB,MAAM;QACxBlC,KAAIC,GAAG,CAAC;IACR,oCAAoC;IACtC,OAAO;QACL,+BAA+B;QAC/BE,QAAQ6B,GAAG,CAACC,cAAc,GAAGE,OAAOD;IACtC;IAEA,OAAOA;AACT"}