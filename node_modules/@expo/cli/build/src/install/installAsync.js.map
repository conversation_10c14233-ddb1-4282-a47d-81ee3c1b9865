{"version": 3, "sources": ["../../../src/install/installAsync.ts"], "sourcesContent": ["import { getConfig, getPackage<PERSON>son } from '@expo/config';\nimport * as PackageManager from '@expo/package-manager';\nimport chalk from 'chalk';\n\nimport { applyPluginsAsync } from './applyPlugins';\nimport { checkPackagesAsync } from './checkPackages';\nimport { installExpoPackageAsync } from './installExpoPackage';\nimport { Options } from './resolveOptions';\nimport * as Log from '../log';\nimport { checkPackagesCompatibility } from './utils/checkPackagesCompatibility';\nimport { getVersionedPackagesAsync } from '../start/doctor/dependencies/getVersionedPackages';\nimport { env } from '../utils/env';\nimport { CommandError } from '../utils/errors';\nimport { findUpProjectRootOrAssert } from '../utils/findUp';\nimport { learnMore } from '../utils/link';\nimport { setNodeEnv } from '../utils/nodeEnv';\nimport { joinWithCommasAnd } from '../utils/strings';\n\n/**\n * Installs versions of specified packages compatible with the current Expo SDK version, or\n * checks/ fixes dependencies in project if they don't match compatible versions specified in bundledNativeModules or versions endpoints.\n *\n * @param packages list of packages to install, if installing specific packages and not checking/ fixing\n * @param options options, including check or fix\n * @param packageManagerArguments arguments to forward to the package manager invoked while installing\n * @returns Promise<void>\n */\nexport async function installAsync(\n  packages: string[],\n  options: Options & { projectRoot?: string },\n  packageManagerArguments: string[] = []\n) {\n  setNodeEnv('development');\n  // Locate the project root based on the process current working directory.\n  // This enables users to run `npx expo install` from a subdirectory of the project.\n  const projectRoot = options?.projectRoot ?? findUpProjectRootOrAssert(process.cwd());\n  require('@expo/env').load(projectRoot);\n\n  // Resolve the package manager used by the project, or based on the provided arguments.\n  const packageManager = PackageManager.createForProject(projectRoot, {\n    npm: options.npm,\n    yarn: options.yarn,\n    bun: options.bun,\n    pnpm: options.pnpm,\n    silent: options.silent,\n    log: Log.log,\n  });\n\n  const expoVersion = findPackageByName(packages, 'expo');\n  const otherPackages = packages.filter((pkg) => pkg !== expoVersion);\n\n  // Abort early when installing `expo@<version>` and other packages with `--fix/--check`\n  if (packageHasVersion(expoVersion) && otherPackages.length && (options.check || options.fix)) {\n    throw new CommandError(\n      'BAD_ARGS',\n      `Cannot install other packages with ${expoVersion} and --fix or --check`\n    );\n  }\n\n  // Only check/fix packages if `expo@<version>` is not requested\n  if (!packageHasVersion(expoVersion) && (options.check || options.fix)) {\n    return await checkPackagesAsync(projectRoot, {\n      packages,\n      options,\n      packageManager,\n      packageManagerArguments,\n    });\n  }\n\n  // note(simek): check out the packages compatibility with New Architecture against RND API\n  if (!env.EXPO_NO_DEPENDENCY_VALIDATION && !env.EXPO_NO_NEW_ARCH_COMPAT_CHECK) {\n    await checkPackagesCompatibility(otherPackages);\n  }\n\n  // Read the project Expo config without plugins.\n  const { exp } = getConfig(projectRoot, {\n    // Sometimes users will add a plugin to the config before installing the library,\n    // this wouldn't work unless we dangerously disable plugin serialization.\n    skipPlugins: true,\n  });\n\n  // Resolve the versioned packages, then install them.\n  return installPackagesAsync(projectRoot, {\n    ...options,\n    packageManager,\n    packages,\n    packageManagerArguments,\n    sdkVersion: exp.sdkVersion!,\n  });\n}\n\n/** Version packages and install in a project. */\nexport async function installPackagesAsync(\n  projectRoot: string,\n  {\n    packages,\n    packageManager,\n    sdkVersion,\n    packageManagerArguments,\n    fix,\n    check,\n    dev,\n  }: Options & {\n    /**\n     * List of packages to version, grouped by the type of dependency.\n     * @example ['uuid', 'react-native-reanimated@latest']\n     */\n    packages: string[];\n    /** Package manager to use when installing the versioned packages. */\n    packageManager: PackageManager.NodePackageManager;\n    /**\n     * SDK to version `packages` for.\n     * @example '44.0.0'\n     */\n    sdkVersion: string;\n    /**\n     * Extra parameters to pass to the `packageManager` when installing versioned packages.\n     * @example ['--no-save']\n     */\n    packageManagerArguments: string[];\n  }\n): Promise<void> {\n  // Read the project Expo config without plugins.\n  const pkg = getPackageJson(projectRoot);\n\n  //assertNotInstallingExcludedPackages(projectRoot, packages, pkg);\n\n  const versioning = await getVersionedPackagesAsync(projectRoot, {\n    packages,\n    // sdkVersion is always defined because we don't skipSDKVersionRequirement in getConfig.\n    sdkVersion,\n    pkg,\n  });\n\n  Log.log(\n    chalk`\\u203A Installing ${\n      versioning.messages.length ? versioning.messages.join(' and ') + ' ' : ''\n    }using {bold ${packageManager.name}}`\n  );\n\n  if (versioning.excludedNativeModules.length) {\n    const alreadyExcluded = versioning.excludedNativeModules.filter(\n      (module) => module.isExcludedFromValidation\n    );\n    const specifiedExactVersion = versioning.excludedNativeModules.filter(\n      (module) => !module.isExcludedFromValidation\n    );\n\n    if (alreadyExcluded.length) {\n      Log.log(\n        chalk`\\u203A Using ${joinWithCommasAnd(\n          alreadyExcluded.map(\n            ({ bundledNativeVersion, name, specifiedVersion }) =>\n              `${specifiedVersion || 'latest'} instead of  ${bundledNativeVersion} for ${name}`\n          )\n        )} because ${\n          alreadyExcluded.length > 1 ? 'they are' : 'it is'\n        } listed in {bold expo.install.exclude} in package.json. ${learnMore(\n          'https://docs.expo.dev/more/expo-cli/#configuring-dependency-validation'\n        )}`\n      );\n    }\n\n    if (specifiedExactVersion.length) {\n      Log.log(\n        chalk`\\u203A Using ${joinWithCommasAnd(\n          specifiedExactVersion.map(\n            ({ bundledNativeVersion, name, specifiedVersion }) =>\n              `${specifiedVersion} instead of ${bundledNativeVersion} for ${name}`\n          )\n        )} because ${\n          specifiedExactVersion.length > 1 ? 'these versions' : 'this version'\n        } was explicitly provided. Packages excluded from dependency validation should be listed in {bold expo.install.exclude} in package.json. ${learnMore(\n          'https://docs.expo.dev/more/expo-cli/#configuring-dependency-validation'\n        )}`\n      );\n    }\n  }\n\n  // `expo` needs to be installed before installing other packages\n  const expoPackage = findPackageByName(packages, 'expo');\n  if (expoPackage) {\n    const postInstallCommand = packages.filter((pkg) => pkg !== expoPackage);\n\n    // Pipe options to the next command\n    if (fix) postInstallCommand.push('--fix');\n    if (check) postInstallCommand.push('--check');\n\n    // Abort after installing `expo`, follow up command is spawn in a new process\n    return await installExpoPackageAsync(projectRoot, {\n      packageManager,\n      packageManagerArguments,\n      expoPackageToInstall: versioning.packages.find((pkg) => pkg.startsWith('expo@'))!,\n      followUpCommandArgs: postInstallCommand,\n    });\n  }\n\n  if (dev) {\n    await packageManager.addDevAsync([...packageManagerArguments, ...versioning.packages]);\n  } else {\n    await packageManager.addAsync([...packageManagerArguments, ...versioning.packages]);\n  }\n\n  await applyPluginsAsync(projectRoot, versioning.packages);\n}\n\n/** Find a package, by name, in the requested packages list (`expo` -> `expo`/`expo@<version>`) */\nfunction findPackageByName(packages: string[], name: string) {\n  return packages.find((pkg) => pkg === name || pkg.startsWith(`${name}@`));\n}\n\n/** Determine if a specific version is requested for a package */\nfunction packageHasVersion(name = '') {\n  return name.includes('@');\n}\n"], "names": ["installAsync", "installPackagesAsync", "packages", "options", "packageManagerArguments", "setNodeEnv", "projectRoot", "findUpProjectRootOrAssert", "process", "cwd", "require", "load", "packageManager", "PackageManager", "createForProject", "npm", "yarn", "bun", "pnpm", "silent", "log", "Log", "expoVersion", "findPackageByName", "otherPackages", "filter", "pkg", "packageHasVersion", "length", "check", "fix", "CommandError", "checkPackagesAsync", "env", "EXPO_NO_DEPENDENCY_VALIDATION", "EXPO_NO_NEW_ARCH_COMPAT_CHECK", "checkPackagesCompatibility", "exp", "getConfig", "skip<PERSON>lug<PERSON>", "sdkVersion", "dev", "getPackageJson", "versioning", "getVersionedPackagesAsync", "chalk", "messages", "join", "name", "excludedNativeModules", "alreadyExcluded", "module", "isExcludedFromValidation", "specifiedExactVersion", "joinWithCommasAnd", "map", "bundledNativeVersion", "specifiedVersion", "learnMore", "expoPackage", "postInstallCommand", "push", "installExpoPackageAsync", "expoPackageToInstall", "find", "startsWith", "followUpCommandArgs", "addDevAsync", "addAsync", "applyPluginsAsync", "includes"], "mappings": ";;;;;;;;;;;IA2BsBA,YAAY;eAAZA;;IAiEAC,oBAAoB;eAApBA;;;;yBA5FoB;;;;;;;iEACV;;;;;;;gEACd;;;;;;8BAEgB;+BACC;oCACK;6DAEnB;4CACsB;sCACD;qBACtB;wBACS;wBACa;sBAChB;yBACC;yBACO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW3B,eAAeD,aACpBE,QAAkB,EAClBC,OAA2C,EAC3CC,0BAAoC,EAAE;IAEtCC,IAAAA,mBAAU,EAAC;IACX,0EAA0E;IAC1E,mFAAmF;IACnF,MAAMC,cAAcH,CAAAA,2BAAAA,QAASG,WAAW,KAAIC,IAAAA,iCAAyB,EAACC,QAAQC,GAAG;IACjFC,QAAQ,aAAaC,IAAI,CAACL;IAE1B,uFAAuF;IACvF,MAAMM,iBAAiBC,kBAAeC,gBAAgB,CAACR,aAAa;QAClES,KAAKZ,QAAQY,GAAG;QAChBC,MAAMb,QAAQa,IAAI;QAClBC,KAAKd,QAAQc,GAAG;QAChBC,MAAMf,QAAQe,IAAI;QAClBC,QAAQhB,QAAQgB,MAAM;QACtBC,KAAKC,KAAID,GAAG;IACd;IAEA,MAAME,cAAcC,kBAAkBrB,UAAU;IAChD,MAAMsB,gBAAgBtB,SAASuB,MAAM,CAAC,CAACC,MAAQA,QAAQJ;IAEvD,uFAAuF;IACvF,IAAIK,kBAAkBL,gBAAgBE,cAAcI,MAAM,IAAKzB,CAAAA,QAAQ0B,KAAK,IAAI1B,QAAQ2B,GAAG,AAAD,GAAI;QAC5F,MAAM,IAAIC,oBAAY,CACpB,YACA,CAAC,mCAAmC,EAAET,YAAY,qBAAqB,CAAC;IAE5E;IAEA,+DAA+D;IAC/D,IAAI,CAACK,kBAAkBL,gBAAiBnB,CAAAA,QAAQ0B,KAAK,IAAI1B,QAAQ2B,GAAG,AAAD,GAAI;QACrE,OAAO,MAAME,IAAAA,iCAAkB,EAAC1B,aAAa;YAC3CJ;YACAC;YACAS;YACAR;QACF;IACF;IAEA,0FAA0F;IAC1F,IAAI,CAAC6B,QAAG,CAACC,6BAA6B,IAAI,CAACD,QAAG,CAACE,6BAA6B,EAAE;QAC5E,MAAMC,IAAAA,sDAA0B,EAACZ;IACnC;IAEA,gDAAgD;IAChD,MAAM,EAAEa,GAAG,EAAE,GAAGC,IAAAA,mBAAS,EAAChC,aAAa;QACrC,iFAAiF;QACjF,yEAAyE;QACzEiC,aAAa;IACf;IAEA,qDAAqD;IACrD,OAAOtC,qBAAqBK,aAAa;QACvC,GAAGH,OAAO;QACVS;QACAV;QACAE;QACAoC,YAAYH,IAAIG,UAAU;IAC5B;AACF;AAGO,eAAevC,qBACpBK,WAAmB,EACnB,EACEJ,QAAQ,EACRU,cAAc,EACd4B,UAAU,EACVpC,uBAAuB,EACvB0B,GAAG,EACHD,KAAK,EACLY,GAAG,EAmBJ;IAED,gDAAgD;IAChD,MAAMf,MAAMgB,IAAAA,wBAAc,EAACpC;IAE3B,kEAAkE;IAElE,MAAMqC,aAAa,MAAMC,IAAAA,+CAAyB,EAACtC,aAAa;QAC9DJ;QACA,wFAAwF;QACxFsC;QACAd;IACF;IAEAL,KAAID,GAAG,CACLyB,IAAAA,gBAAK,CAAA,CAAC,kBAAkB,EACtBF,WAAWG,QAAQ,CAAClB,MAAM,GAAGe,WAAWG,QAAQ,CAACC,IAAI,CAAC,WAAW,MAAM,GACxE,YAAY,EAAEnC,eAAeoC,IAAI,CAAC,CAAC,CAAC;IAGvC,IAAIL,WAAWM,qBAAqB,CAACrB,MAAM,EAAE;QAC3C,MAAMsB,kBAAkBP,WAAWM,qBAAqB,CAACxB,MAAM,CAC7D,CAAC0B,SAAWA,OAAOC,wBAAwB;QAE7C,MAAMC,wBAAwBV,WAAWM,qBAAqB,CAACxB,MAAM,CACnE,CAAC0B,SAAW,CAACA,OAAOC,wBAAwB;QAG9C,IAAIF,gBAAgBtB,MAAM,EAAE;YAC1BP,KAAID,GAAG,CACLyB,IAAAA,gBAAK,CAAA,CAAC,aAAa,EAAES,IAAAA,0BAAiB,EACpCJ,gBAAgBK,GAAG,CACjB,CAAC,EAAEC,oBAAoB,EAAER,IAAI,EAAES,gBAAgB,EAAE,GAC/C,GAAGA,oBAAoB,SAAS,aAAa,EAAED,qBAAqB,KAAK,EAAER,MAAM,GAErF,SAAS,EACTE,gBAAgBtB,MAAM,GAAG,IAAI,aAAa,QAC3C,wDAAwD,EAAE8B,IAAAA,eAAS,EAClE,0EACA,CAAC;QAEP;QAEA,IAAIL,sBAAsBzB,MAAM,EAAE;YAChCP,KAAID,GAAG,CACLyB,IAAAA,gBAAK,CAAA,CAAC,aAAa,EAAES,IAAAA,0BAAiB,EACpCD,sBAAsBE,GAAG,CACvB,CAAC,EAAEC,oBAAoB,EAAER,IAAI,EAAES,gBAAgB,EAAE,GAC/C,GAAGA,iBAAiB,YAAY,EAAED,qBAAqB,KAAK,EAAER,MAAM,GAExE,SAAS,EACTK,sBAAsBzB,MAAM,GAAG,IAAI,mBAAmB,eACvD,wIAAwI,EAAE8B,IAAAA,eAAS,EAClJ,0EACA,CAAC;QAEP;IACF;IAEA,gEAAgE;IAChE,MAAMC,cAAcpC,kBAAkBrB,UAAU;IAChD,IAAIyD,aAAa;QACf,MAAMC,qBAAqB1D,SAASuB,MAAM,CAAC,CAACC,MAAQA,QAAQiC;QAE5D,mCAAmC;QACnC,IAAI7B,KAAK8B,mBAAmBC,IAAI,CAAC;QACjC,IAAIhC,OAAO+B,mBAAmBC,IAAI,CAAC;QAEnC,6EAA6E;QAC7E,OAAO,MAAMC,IAAAA,2CAAuB,EAACxD,aAAa;YAChDM;YACAR;YACA2D,sBAAsBpB,WAAWzC,QAAQ,CAAC8D,IAAI,CAAC,CAACtC,MAAQA,IAAIuC,UAAU,CAAC;YACvEC,qBAAqBN;QACvB;IACF;IAEA,IAAInB,KAAK;QACP,MAAM7B,eAAeuD,WAAW,CAAC;eAAI/D;eAA4BuC,WAAWzC,QAAQ;SAAC;IACvF,OAAO;QACL,MAAMU,eAAewD,QAAQ,CAAC;eAAIhE;eAA4BuC,WAAWzC,QAAQ;SAAC;IACpF;IAEA,MAAMmE,IAAAA,+BAAiB,EAAC/D,aAAaqC,WAAWzC,QAAQ;AAC1D;AAEA,gGAAgG,GAChG,SAASqB,kBAAkBrB,QAAkB,EAAE8C,IAAY;IACzD,OAAO9C,SAAS8D,IAAI,CAAC,CAACtC,MAAQA,QAAQsB,QAAQtB,IAAIuC,UAAU,CAAC,GAAGjB,KAAK,CAAC,CAAC;AACzE;AAEA,+DAA+D,GAC/D,SAASrB,kBAAkBqB,OAAO,EAAE;IAClC,OAAOA,KAAKsB,QAAQ,CAAC;AACvB"}