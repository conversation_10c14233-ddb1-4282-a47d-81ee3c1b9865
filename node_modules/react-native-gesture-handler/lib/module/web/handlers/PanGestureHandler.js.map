{"version": 3, "names": ["State", "DEFAULT_TOUCH_SLOP", "WheelDevice", "Gesture<PERSON>andler", "DEFAULT_MIN_POINTERS", "DEFAULT_MAX_POINTERS", "DEFAULT_MIN_DIST_SQ", "PanGestureHandler", "customActivationProperties", "velocityX", "velocityY", "minDistSq", "activeOffsetXStart", "Number", "MAX_SAFE_INTEGER", "activeOffsetXEnd", "MIN_SAFE_INTEGER", "failOffsetXStart", "failOffsetXEnd", "activeOffsetYStart", "activeOffsetYEnd", "failOffsetYStart", "failOffsetYEnd", "minVelocityX", "minVelocityY", "minVelocitySq", "minPointers", "maxPointers", "startX", "startY", "offsetX", "offsetY", "lastX", "lastY", "activateAfterLongPress", "activationTimeout", "enableTrackpadTwoFingerGesture", "endWheelTimeout", "wheelDevice", "UNDETERMINED", "updateGestureConfig", "enabled", "props", "resetConfig", "checkCustomActivationCriteria", "config", "minDist", "undefined", "hasCustomActivationCriteria", "minVelocity", "transformNativeEvent", "translationX", "getTranslationX", "translationY", "getTranslationY", "isNaN", "stylusData", "clearActivationTimeout", "clearTimeout", "onPointerDown", "event", "isButtonInConfig", "button", "tracker", "addToTracker", "lastCoords", "getAbsoluteCoordsAverage", "x", "y", "tryBegin", "<PERSON><PERSON><PERSON>", "tryToSendTouchEvent", "onPointerAdd", "trackedPointersCount", "state", "ACTIVE", "cancel", "fail", "onPointerUp", "removeFromTracker", "pointerId", "end", "resetProgress", "onPointerRemove", "onPointerMove", "track", "velocity", "getVelocity", "onPointerOutOfBounds", "shouldCancelWhenOutside", "scheduleWheelEnd", "setTimeout", "onWheel", "MOUSE", "wheelDeltaY", "TOUCHPAD", "begin", "activate", "tryToSendMoveEvent", "shouldActivate", "dx", "dy", "distanceSq", "vx", "vy", "velocitySq", "shouldFail", "BEGAN", "force", "onCancel", "onReset"], "sourceRoot": "../../../../src", "sources": ["web/handlers/PanGestureHandler.ts"], "mappings": ";;AAAA,SAASA,KAAK,QAAQ,aAAa;AACnC,SAASC,kBAAkB,QAAQ,cAAc;AACjD,SAA2CC,WAAW,QAAQ,eAAe;AAE7E,OAAOC,cAAc,MAAM,kBAAkB;AAE7C,MAAMC,oBAAoB,GAAG,CAAC;AAC9B,MAAMC,oBAAoB,GAAG,EAAE;AAC/B,MAAMC,mBAAmB,GAAGL,kBAAkB,GAAGA,kBAAkB;AAEnE,eAAe,MAAMM,iBAAiB,SAASJ,cAAc,CAAC;EAC3CK,0BAA0B,GAAa,CACtD,oBAAoB,EACpB,kBAAkB,EAClB,kBAAkB,EAClB,gBAAgB,EAChB,oBAAoB,EACpB,kBAAkB,EAClB,kBAAkB,EAClB,gBAAgB,EAChB,cAAc,EACd,cAAc,EACd,aAAa,CACd;EAEMC,SAAS,GAAG,CAAC;EACbC,SAAS,GAAG,CAAC;EAEZC,SAAS,GAAGL,mBAAmB;EAE/BM,kBAAkB,GAAG,CAACC,MAAM,CAACC,gBAAgB;EAC7CC,gBAAgB,GAAGF,MAAM,CAACG,gBAAgB;EAC1CC,gBAAgB,GAAGJ,MAAM,CAACG,gBAAgB;EAC1CE,cAAc,GAAGL,MAAM,CAACC,gBAAgB;EAExCK,kBAAkB,GAAGN,MAAM,CAACC,gBAAgB;EAC5CM,gBAAgB,GAAGP,MAAM,CAACG,gBAAgB;EAC1CK,gBAAgB,GAAGR,MAAM,CAACG,gBAAgB;EAC1CM,cAAc,GAAGT,MAAM,CAACC,gBAAgB;EAExCS,YAAY,GAAGV,MAAM,CAACC,gBAAgB;EACtCU,YAAY,GAAGX,MAAM,CAACC,gBAAgB;EACtCW,aAAa,GAAGZ,MAAM,CAACC,gBAAgB;EAEvCY,WAAW,GAAGtB,oBAAoB;EAClCuB,WAAW,GAAGtB,oBAAoB;EAElCuB,MAAM,GAAG,CAAC;EACVC,MAAM,GAAG,CAAC;EACVC,OAAO,GAAG,CAAC;EACXC,OAAO,GAAG,CAAC;EACXC,KAAK,GAAG,CAAC;EACTC,KAAK,GAAG,CAAC;EAITC,sBAAsB,GAAG,CAAC;EAC1BC,iBAAiB,GAAG,CAAC;EAErBC,8BAA8B,GAAG,KAAK;EACtCC,eAAe,GAAG,CAAC;EACnBC,WAAW,GAAGpC,WAAW,CAACqC,YAAY;EAEvCC,mBAAmBA,CAAC;IAAEC,OAAO,GAAG,IAAI;IAAE,GAAGC;EAAc,CAAC,EAAQ;IACrE,IAAI,CAACC,WAAW,CAAC,CAAC;IAElB,KAAK,CAACH,mBAAmB,CAAC;MAAEC,OAAO,EAAEA,OAAO;MAAE,GAAGC;IAAM,CAAC,CAAC;IACzD,IAAI,CAACE,6BAA6B,CAAC,IAAI,CAACpC,0BAA0B,CAAC;IAEnE,IAAI,IAAI,CAACqC,MAAM,CAACC,OAAO,KAAKC,SAAS,EAAE;MACrC,IAAI,CAACpC,SAAS,GAAG,IAAI,CAACkC,MAAM,CAACC,OAAO,GAAG,IAAI,CAACD,MAAM,CAACC,OAAO;IAC5D,CAAC,MAAM,IAAI,IAAI,CAACE,2BAA2B,EAAE;MAC3C,IAAI,CAACrC,SAAS,GAAGE,MAAM,CAACC,gBAAgB;IAC1C;IAEA,IAAI,IAAI,CAAC+B,MAAM,CAACnB,WAAW,KAAKqB,SAAS,EAAE;MACzC,IAAI,CAACrB,WAAW,GAAG,IAAI,CAACmB,MAAM,CAACnB,WAAW;IAC5C;IAEA,IAAI,IAAI,CAACmB,MAAM,CAAClB,WAAW,KAAKoB,SAAS,EAAE;MACzC,IAAI,CAACpB,WAAW,GAAG,IAAI,CAACkB,MAAM,CAAClB,WAAW;IAC5C;IAEA,IAAI,IAAI,CAACkB,MAAM,CAACI,WAAW,KAAKF,SAAS,EAAE;MACzC,IAAI,CAACxB,YAAY,GAAG,IAAI,CAACsB,MAAM,CAACI,WAAW;MAC3C,IAAI,CAACzB,YAAY,GAAG,IAAI,CAACqB,MAAM,CAACI,WAAW;IAC7C;IAEA,IAAI,IAAI,CAACJ,MAAM,CAACtB,YAAY,KAAKwB,SAAS,EAAE;MAC1C,IAAI,CAACxB,YAAY,GAAG,IAAI,CAACsB,MAAM,CAACtB,YAAY;IAC9C;IAEA,IAAI,IAAI,CAACsB,MAAM,CAACrB,YAAY,KAAKuB,SAAS,EAAE;MAC1C,IAAI,CAACvB,YAAY,GAAG,IAAI,CAACqB,MAAM,CAACrB,YAAY;IAC9C;IAEA,IAAI,IAAI,CAACqB,MAAM,CAACX,sBAAsB,KAAKa,SAAS,EAAE;MACpD,IAAI,CAACb,sBAAsB,GAAG,IAAI,CAACW,MAAM,CAACX,sBAAsB;IAClE;IAEA,IAAI,IAAI,CAACW,MAAM,CAACjC,kBAAkB,KAAKmC,SAAS,EAAE;MAChD,IAAI,CAACnC,kBAAkB,GAAG,IAAI,CAACiC,MAAM,CAACjC,kBAAkB;MAExD,IAAI,IAAI,CAACiC,MAAM,CAAC9B,gBAAgB,KAAKgC,SAAS,EAAE;QAC9C,IAAI,CAAChC,gBAAgB,GAAGF,MAAM,CAACC,gBAAgB;MACjD;IACF;IAEA,IAAI,IAAI,CAAC+B,MAAM,CAAC9B,gBAAgB,KAAKgC,SAAS,EAAE;MAC9C,IAAI,CAAChC,gBAAgB,GAAG,IAAI,CAAC8B,MAAM,CAAC9B,gBAAgB;MAEpD,IAAI,IAAI,CAAC8B,MAAM,CAACjC,kBAAkB,KAAKmC,SAAS,EAAE;QAChD,IAAI,CAACnC,kBAAkB,GAAGC,MAAM,CAACG,gBAAgB;MACnD;IACF;IAEA,IAAI,IAAI,CAAC6B,MAAM,CAAC5B,gBAAgB,KAAK8B,SAAS,EAAE;MAC9C,IAAI,CAAC9B,gBAAgB,GAAG,IAAI,CAAC4B,MAAM,CAAC5B,gBAAgB;MAEpD,IAAI,IAAI,CAAC4B,MAAM,CAAC3B,cAAc,KAAK6B,SAAS,EAAE;QAC5C,IAAI,CAAC7B,cAAc,GAAGL,MAAM,CAACC,gBAAgB;MAC/C;IACF;IAEA,IAAI,IAAI,CAAC+B,MAAM,CAAC3B,cAAc,KAAK6B,SAAS,EAAE;MAC5C,IAAI,CAAC7B,cAAc,GAAG,IAAI,CAAC2B,MAAM,CAAC3B,cAAc;MAEhD,IAAI,IAAI,CAAC2B,MAAM,CAAC5B,gBAAgB,KAAK8B,SAAS,EAAE;QAC9C,IAAI,CAAC9B,gBAAgB,GAAGJ,MAAM,CAACG,gBAAgB;MACjD;IACF;IAEA,IAAI,IAAI,CAAC6B,MAAM,CAAC1B,kBAAkB,KAAK4B,SAAS,EAAE;MAChD,IAAI,CAAC5B,kBAAkB,GAAG,IAAI,CAAC0B,MAAM,CAAC1B,kBAAkB;MAExD,IAAI,IAAI,CAAC0B,MAAM,CAACzB,gBAAgB,KAAK2B,SAAS,EAAE;QAC9C,IAAI,CAAC3B,gBAAgB,GAAGP,MAAM,CAACC,gBAAgB;MACjD;IACF;IAEA,IAAI,IAAI,CAAC+B,MAAM,CAACzB,gBAAgB,KAAK2B,SAAS,EAAE;MAC9C,IAAI,CAAC3B,gBAAgB,GAAG,IAAI,CAACyB,MAAM,CAACzB,gBAAgB;MAEpD,IAAI,IAAI,CAACyB,MAAM,CAAC1B,kBAAkB,KAAK4B,SAAS,EAAE;QAChD,IAAI,CAAC5B,kBAAkB,GAAGN,MAAM,CAACG,gBAAgB;MACnD;IACF;IAEA,IAAI,IAAI,CAAC6B,MAAM,CAACxB,gBAAgB,KAAK0B,SAAS,EAAE;MAC9C,IAAI,CAAC1B,gBAAgB,GAAG,IAAI,CAACwB,MAAM,CAACxB,gBAAgB;MAEpD,IAAI,IAAI,CAACwB,MAAM,CAACvB,cAAc,KAAKyB,SAAS,EAAE;QAC5C,IAAI,CAACzB,cAAc,GAAGT,MAAM,CAACC,gBAAgB;MAC/C;IACF;IAEA,IAAI,IAAI,CAAC+B,MAAM,CAACvB,cAAc,KAAKyB,SAAS,EAAE;MAC5C,IAAI,CAACzB,cAAc,GAAG,IAAI,CAACuB,MAAM,CAACvB,cAAc;MAEhD,IAAI,IAAI,CAACuB,MAAM,CAACxB,gBAAgB,KAAK0B,SAAS,EAAE;QAC9C,IAAI,CAAC1B,gBAAgB,GAAGR,MAAM,CAACG,gBAAgB;MACjD;IACF;IAEA,IAAI,IAAI,CAAC6B,MAAM,CAACT,8BAA8B,KAAKW,SAAS,EAAE;MAC5D,IAAI,CAACX,8BAA8B,GACjC,IAAI,CAACS,MAAM,CAACT,8BAA8B;IAC9C;EACF;EAEUO,WAAWA,CAAA,EAAS;IAC5B,KAAK,CAACA,WAAW,CAAC,CAAC;IAEnB,IAAI,CAAC/B,kBAAkB,GAAG,CAACC,MAAM,CAACC,gBAAgB;IAClD,IAAI,CAACC,gBAAgB,GAAGF,MAAM,CAACG,gBAAgB;IAC/C,IAAI,CAACC,gBAAgB,GAAGJ,MAAM,CAACG,gBAAgB;IAC/C,IAAI,CAACE,cAAc,GAAGL,MAAM,CAACC,gBAAgB;IAE7C,IAAI,CAACK,kBAAkB,GAAGN,MAAM,CAACC,gBAAgB;IACjD,IAAI,CAACM,gBAAgB,GAAGP,MAAM,CAACG,gBAAgB;IAC/C,IAAI,CAACK,gBAAgB,GAAGR,MAAM,CAACG,gBAAgB;IAC/C,IAAI,CAACM,cAAc,GAAGT,MAAM,CAACC,gBAAgB;IAE7C,IAAI,CAACS,YAAY,GAAGV,MAAM,CAACC,gBAAgB;IAC3C,IAAI,CAACU,YAAY,GAAGX,MAAM,CAACC,gBAAgB;IAC3C,IAAI,CAACW,aAAa,GAAGZ,MAAM,CAACC,gBAAgB;IAE5C,IAAI,CAACH,SAAS,GAAGL,mBAAmB;IAEpC,IAAI,CAACoB,WAAW,GAAGtB,oBAAoB;IACvC,IAAI,CAACuB,WAAW,GAAGtB,oBAAoB;IAEvC,IAAI,CAAC6B,sBAAsB,GAAG,CAAC;EACjC;EAEUgB,oBAAoBA,CAAA,EAAG;IAC/B,MAAMC,YAAoB,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;IACnD,MAAMC,YAAoB,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;IAEnD,OAAO;MACL,GAAG,KAAK,CAACJ,oBAAoB,CAAC,CAAC;MAC/BC,YAAY,EAAEI,KAAK,CAACJ,YAAY,CAAC,GAAG,CAAC,GAAGA,YAAY;MACpDE,YAAY,EAAEE,KAAK,CAACF,YAAY,CAAC,GAAG,CAAC,GAAGA,YAAY;MACpD5C,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBC,SAAS,EAAE,IAAI,CAACA,SAAS;MACzB8C,UAAU,EAAE,IAAI,CAACA;IACnB,CAAC;EACH;EAEQJ,eAAeA,CAAA,EAAW;IAChC,OAAO,IAAI,CAACpB,KAAK,GAAG,IAAI,CAACJ,MAAM,GAAG,IAAI,CAACE,OAAO;EAChD;EACQwB,eAAeA,CAAA,EAAW;IAChC,OAAO,IAAI,CAACrB,KAAK,GAAG,IAAI,CAACJ,MAAM,GAAG,IAAI,CAACE,OAAO;EAChD;EAEQ0B,sBAAsBA,CAAA,EAAS;IACrCC,YAAY,CAAC,IAAI,CAACvB,iBAAiB,CAAC;EACtC;;EAEA;EACUwB,aAAaA,CAACC,KAAmB,EAAQ;IACjD,IAAI,CAAC,IAAI,CAACC,gBAAgB,CAACD,KAAK,CAACE,MAAM,CAAC,EAAE;MACxC;IACF;IAEA,IAAI,CAACC,OAAO,CAACC,YAAY,CAACJ,KAAK,CAAC;IAChC,IAAI,CAACJ,UAAU,GAAGI,KAAK,CAACJ,UAAU;IAElC,KAAK,CAACG,aAAa,CAACC,KAAK,CAAC;IAE1B,MAAMK,UAAU,GAAG,IAAI,CAACF,OAAO,CAACG,wBAAwB,CAAC,CAAC;IAC1D,IAAI,CAAClC,KAAK,GAAGiC,UAAU,CAACE,CAAC;IACzB,IAAI,CAAClC,KAAK,GAAGgC,UAAU,CAACG,CAAC;IAEzB,IAAI,CAACxC,MAAM,GAAG,IAAI,CAACI,KAAK;IACxB,IAAI,CAACH,MAAM,GAAG,IAAI,CAACI,KAAK;IAExB,IAAI,CAACoC,QAAQ,CAACT,KAAK,CAAC;IACpB,IAAI,CAACU,UAAU,CAAC,CAAC;IAEjB,IAAI,CAACC,mBAAmB,CAACX,KAAK,CAAC;EACjC;EAEUY,YAAYA,CAACZ,KAAmB,EAAQ;IAChD,IAAI,CAACG,OAAO,CAACC,YAAY,CAACJ,KAAK,CAAC;IAChC,KAAK,CAACY,YAAY,CAACZ,KAAK,CAAC;IACzB,IAAI,CAACS,QAAQ,CAACT,KAAK,CAAC;IAEpB,IAAI,CAAC9B,OAAO,IAAI,IAAI,CAACE,KAAK,GAAG,IAAI,CAACJ,MAAM;IACxC,IAAI,CAACG,OAAO,IAAI,IAAI,CAACE,KAAK,GAAG,IAAI,CAACJ,MAAM;IAExC,MAAMoC,UAAU,GAAG,IAAI,CAACF,OAAO,CAACG,wBAAwB,CAAC,CAAC;IAC1D,IAAI,CAAClC,KAAK,GAAGiC,UAAU,CAACE,CAAC;IACzB,IAAI,CAAClC,KAAK,GAAGgC,UAAU,CAACG,CAAC;IAEzB,IAAI,CAACxC,MAAM,GAAG,IAAI,CAACI,KAAK;IACxB,IAAI,CAACH,MAAM,GAAG,IAAI,CAACI,KAAK;IAExB,IAAI,IAAI,CAAC8B,OAAO,CAACU,oBAAoB,GAAG,IAAI,CAAC9C,WAAW,EAAE;MACxD,IAAI,IAAI,CAAC+C,KAAK,KAAK1E,KAAK,CAAC2E,MAAM,EAAE;QAC/B,IAAI,CAACC,MAAM,CAAC,CAAC;MACf,CAAC,MAAM;QACL,IAAI,CAACC,IAAI,CAAC,CAAC;MACb;IACF,CAAC,MAAM;MACL,IAAI,CAACP,UAAU,CAAC,CAAC;IACnB;EACF;EAEUQ,WAAWA,CAAClB,KAAmB,EAAQ;IAC/C,IAAI,CAACJ,UAAU,GAAGI,KAAK,CAACJ,UAAU;IAElC,KAAK,CAACsB,WAAW,CAAClB,KAAK,CAAC;IACxB,IAAI,IAAI,CAACc,KAAK,KAAK1E,KAAK,CAAC2E,MAAM,EAAE;MAC/B,MAAMV,UAAU,GAAG,IAAI,CAACF,OAAO,CAACG,wBAAwB,CAAC,CAAC;MAC1D,IAAI,CAAClC,KAAK,GAAGiC,UAAU,CAACE,CAAC;MACzB,IAAI,CAAClC,KAAK,GAAGgC,UAAU,CAACG,CAAC;IAC3B;IAEA,IAAI,CAACL,OAAO,CAACgB,iBAAiB,CAACnB,KAAK,CAACoB,SAAS,CAAC;IAE/C,IAAI,IAAI,CAACjB,OAAO,CAACU,oBAAoB,KAAK,CAAC,EAAE;MAC3C,IAAI,CAAChB,sBAAsB,CAAC,CAAC;IAC/B;IAEA,IAAI,IAAI,CAACiB,KAAK,KAAK1E,KAAK,CAAC2E,MAAM,EAAE;MAC/B,IAAI,CAACM,GAAG,CAAC,CAAC;IACZ,CAAC,MAAM;MACL,IAAI,CAACC,aAAa,CAAC,CAAC;MACpB,IAAI,CAACL,IAAI,CAAC,CAAC;IACb;EACF;EAEUM,eAAeA,CAACvB,KAAmB,EAAQ;IACnD,KAAK,CAACuB,eAAe,CAACvB,KAAK,CAAC;IAC5B,IAAI,CAACG,OAAO,CAACgB,iBAAiB,CAACnB,KAAK,CAACoB,SAAS,CAAC;IAE/C,IAAI,CAAClD,OAAO,IAAI,IAAI,CAACE,KAAK,GAAG,IAAI,CAACJ,MAAM;IACxC,IAAI,CAACG,OAAO,IAAI,IAAI,CAACE,KAAK,GAAG,IAAI,CAACJ,MAAM;IAExC,MAAMoC,UAAU,GAAG,IAAI,CAACF,OAAO,CAACG,wBAAwB,CAAC,CAAC;IAC1D,IAAI,CAAClC,KAAK,GAAGiC,UAAU,CAACE,CAAC;IACzB,IAAI,CAAClC,KAAK,GAAGgC,UAAU,CAACG,CAAC;IAEzB,IAAI,CAACxC,MAAM,GAAG,IAAI,CAACI,KAAK;IACxB,IAAI,CAACH,MAAM,GAAG,IAAI,CAACI,KAAK;IAExB,IACE,EACE,IAAI,CAACyC,KAAK,KAAK1E,KAAK,CAAC2E,MAAM,IAC3B,IAAI,CAACZ,OAAO,CAACU,oBAAoB,GAAG,IAAI,CAAC/C,WAAW,CACrD,EACD;MACA,IAAI,CAAC4C,UAAU,CAAC,CAAC;IACnB;EACF;EAEUc,aAAaA,CAACxB,KAAmB,EAAQ;IACjD,IAAI,CAACG,OAAO,CAACsB,KAAK,CAACzB,KAAK,CAAC;IACzB,IAAI,CAACJ,UAAU,GAAGI,KAAK,CAACJ,UAAU;IAElC,MAAMS,UAAU,GAAG,IAAI,CAACF,OAAO,CAACG,wBAAwB,CAAC,CAAC;IAC1D,IAAI,CAAClC,KAAK,GAAGiC,UAAU,CAACE,CAAC;IACzB,IAAI,CAAClC,KAAK,GAAGgC,UAAU,CAACG,CAAC;IAEzB,MAAMkB,QAAQ,GAAG,IAAI,CAACvB,OAAO,CAACwB,WAAW,CAAC3B,KAAK,CAACoB,SAAS,CAAC;IAC1D,IAAI,CAACvE,SAAS,GAAG6E,QAAQ,CAACnB,CAAC;IAC3B,IAAI,CAACzD,SAAS,GAAG4E,QAAQ,CAAClB,CAAC;IAE3B,IAAI,CAACE,UAAU,CAAC,CAAC;IAEjB,KAAK,CAACc,aAAa,CAACxB,KAAK,CAAC;EAC5B;EAEU4B,oBAAoBA,CAAC5B,KAAmB,EAAQ;IACxD,IAAI,IAAI,CAAC6B,uBAAuB,EAAE;MAChC;IACF;IAEA,IAAI,CAAC1B,OAAO,CAACsB,KAAK,CAACzB,KAAK,CAAC;IACzB,IAAI,CAACJ,UAAU,GAAGI,KAAK,CAACJ,UAAU;IAElC,MAAMS,UAAU,GAAG,IAAI,CAACF,OAAO,CAACG,wBAAwB,CAAC,CAAC;IAC1D,IAAI,CAAClC,KAAK,GAAGiC,UAAU,CAACE,CAAC;IACzB,IAAI,CAAClC,KAAK,GAAGgC,UAAU,CAACG,CAAC;IAEzB,MAAMkB,QAAQ,GAAG,IAAI,CAACvB,OAAO,CAACwB,WAAW,CAAC3B,KAAK,CAACoB,SAAS,CAAC;IAC1D,IAAI,CAACvE,SAAS,GAAG6E,QAAQ,CAACnB,CAAC;IAC3B,IAAI,CAACzD,SAAS,GAAG4E,QAAQ,CAAClB,CAAC;IAE3B,IAAI,CAACE,UAAU,CAAC,CAAC;IAEjB,IAAI,IAAI,CAACI,KAAK,KAAK1E,KAAK,CAAC2E,MAAM,EAAE;MAC/B,KAAK,CAACa,oBAAoB,CAAC5B,KAAK,CAAC;IACnC;EACF;EAEQ8B,gBAAgBA,CAAC9B,KAAmB,EAAE;IAC5CF,YAAY,CAAC,IAAI,CAACrB,eAAe,CAAC;IAElC,IAAI,CAACA,eAAe,GAAGsD,UAAU,CAAC,MAAM;MACtC,IAAI,IAAI,CAACjB,KAAK,KAAK1E,KAAK,CAAC2E,MAAM,EAAE;QAC/B,IAAI,CAACM,GAAG,CAAC,CAAC;QACV,IAAI,CAAClB,OAAO,CAACgB,iBAAiB,CAACnB,KAAK,CAACoB,SAAS,CAAC;QAC/C,IAAI,CAACN,KAAK,GAAG1E,KAAK,CAACuC,YAAY;MACjC;MAEA,IAAI,CAACD,WAAW,GAAGpC,WAAW,CAACqC,YAAY;IAC7C,CAAC,EAAE,EAAE,CAAC;EACR;EAEUqD,OAAOA,CAAChC,KAAmB,EAAQ;IAC3C,IACE,IAAI,CAACtB,WAAW,KAAKpC,WAAW,CAAC2F,KAAK,IACtC,CAAC,IAAI,CAACzD,8BAA8B,EACpC;MACA;IACF;IAEA,IAAI,IAAI,CAACsC,KAAK,KAAK1E,KAAK,CAACuC,YAAY,EAAE;MACrC,IAAI,CAACD,WAAW,GACdsB,KAAK,CAACkC,WAAW,GAAI,GAAG,KAAK,CAAC,GAC1B5F,WAAW,CAAC6F,QAAQ,GACpB7F,WAAW,CAAC2F,KAAK;MAEvB,IAAI,IAAI,CAACvD,WAAW,KAAKpC,WAAW,CAAC2F,KAAK,EAAE;QAC1C,IAAI,CAACH,gBAAgB,CAAC9B,KAAK,CAAC;QAC5B;MACF;MAEA,IAAI,CAACG,OAAO,CAACC,YAAY,CAACJ,KAAK,CAAC;MAEhC,MAAMK,UAAU,GAAG,IAAI,CAACF,OAAO,CAACG,wBAAwB,CAAC,CAAC;MAC1D,IAAI,CAAClC,KAAK,GAAGiC,UAAU,CAACE,CAAC;MACzB,IAAI,CAAClC,KAAK,GAAGgC,UAAU,CAACG,CAAC;MAEzB,IAAI,CAACxC,MAAM,GAAG,IAAI,CAACI,KAAK;MACxB,IAAI,CAACH,MAAM,GAAG,IAAI,CAACI,KAAK;MAExB,IAAI,CAAC+D,KAAK,CAAC,CAAC;MACZ,IAAI,CAACC,QAAQ,CAAC,CAAC;IACjB;IACA,IAAI,CAAClC,OAAO,CAACsB,KAAK,CAACzB,KAAK,CAAC;IAEzB,MAAMK,UAAU,GAAG,IAAI,CAACF,OAAO,CAACG,wBAAwB,CAAC,CAAC;IAC1D,IAAI,CAAClC,KAAK,GAAGiC,UAAU,CAACE,CAAC;IACzB,IAAI,CAAClC,KAAK,GAAGgC,UAAU,CAACG,CAAC;IAEzB,MAAMkB,QAAQ,GAAG,IAAI,CAACvB,OAAO,CAACwB,WAAW,CAAC3B,KAAK,CAACoB,SAAS,CAAC;IAC1D,IAAI,CAACvE,SAAS,GAAG6E,QAAQ,CAACnB,CAAC;IAC3B,IAAI,CAACzD,SAAS,GAAG4E,QAAQ,CAAClB,CAAC;IAE3B,IAAI,CAAC8B,kBAAkB,CAAC,KAAK,EAAEtC,KAAK,CAAC;IACrC,IAAI,CAAC8B,gBAAgB,CAAC9B,KAAK,CAAC;EAC9B;EAEQuC,cAAcA,CAAA,EAAY;IAChC,MAAMC,EAAU,GAAG,IAAI,CAAChD,eAAe,CAAC,CAAC;IAEzC,IACE,IAAI,CAACxC,kBAAkB,KAAKC,MAAM,CAACC,gBAAgB,IACnDsF,EAAE,GAAG,IAAI,CAACxF,kBAAkB,EAC5B;MACA,OAAO,IAAI;IACb;IAEA,IACE,IAAI,CAACG,gBAAgB,KAAKF,MAAM,CAACG,gBAAgB,IACjDoF,EAAE,GAAG,IAAI,CAACrF,gBAAgB,EAC1B;MACA,OAAO,IAAI;IACb;IAEA,MAAMsF,EAAU,GAAG,IAAI,CAAC/C,eAAe,CAAC,CAAC;IAEzC,IACE,IAAI,CAACnC,kBAAkB,KAAKN,MAAM,CAACC,gBAAgB,IACnDuF,EAAE,GAAG,IAAI,CAAClF,kBAAkB,EAC5B;MACA,OAAO,IAAI;IACb;IAEA,IACE,IAAI,CAACC,gBAAgB,KAAKP,MAAM,CAACG,gBAAgB,IACjDqF,EAAE,GAAG,IAAI,CAACjF,gBAAgB,EAC1B;MACA,OAAO,IAAI;IACb;IAEA,MAAMkF,UAAkB,GAAGF,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE;IAE5C,IACE,IAAI,CAAC1F,SAAS,KAAKE,MAAM,CAACC,gBAAgB,IAC1CwF,UAAU,IAAI,IAAI,CAAC3F,SAAS,EAC5B;MACA,OAAO,IAAI;IACb;IAEA,MAAM4F,EAAU,GAAG,IAAI,CAAC9F,SAAS;IAEjC,IACE,IAAI,CAACc,YAAY,KAAKV,MAAM,CAACC,gBAAgB,KAC3C,IAAI,CAACS,YAAY,GAAG,CAAC,IAAIgF,EAAE,IAAI,IAAI,CAAChF,YAAY,IAC/C,IAAI,CAACA,YAAY,IAAI,CAAC,IAAI,IAAI,CAACA,YAAY,IAAIgF,EAAG,CAAC,EACtD;MACA,OAAO,IAAI;IACb;IAEA,MAAMC,EAAU,GAAG,IAAI,CAAC9F,SAAS;IACjC,IACE,IAAI,CAACc,YAAY,KAAKX,MAAM,CAACC,gBAAgB,KAC3C,IAAI,CAACU,YAAY,GAAG,CAAC,IAAIgF,EAAE,IAAI,IAAI,CAAChF,YAAY,IAC/C,IAAI,CAACA,YAAY,IAAI,CAAC,IAAI,IAAI,CAACA,YAAY,IAAIgF,EAAG,CAAC,EACtD;MACA,OAAO,IAAI;IACb;IAEA,MAAMC,UAAkB,GAAGF,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE;IAE5C,OACE,IAAI,CAAC/E,aAAa,KAAKZ,MAAM,CAACC,gBAAgB,IAC9C2F,UAAU,IAAI,IAAI,CAAChF,aAAa;EAEpC;EAEQiF,UAAUA,CAAA,EAAY;IAC5B,MAAMN,EAAU,GAAG,IAAI,CAAChD,eAAe,CAAC,CAAC;IACzC,MAAMiD,EAAU,GAAG,IAAI,CAAC/C,eAAe,CAAC,CAAC;IACzC,MAAMgD,UAAU,GAAGF,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE;IAEpC,IAAI,IAAI,CAACnE,sBAAsB,GAAG,CAAC,IAAIoE,UAAU,GAAGhG,mBAAmB,EAAE;MACvE,IAAI,CAACmD,sBAAsB,CAAC,CAAC;MAC7B,OAAO,IAAI;IACb;IAEA,IACE,IAAI,CAACxC,gBAAgB,KAAKJ,MAAM,CAACG,gBAAgB,IACjDoF,EAAE,GAAG,IAAI,CAACnF,gBAAgB,EAC1B;MACA,OAAO,IAAI;IACb;IAEA,IACE,IAAI,CAACC,cAAc,KAAKL,MAAM,CAACC,gBAAgB,IAC/CsF,EAAE,GAAG,IAAI,CAAClF,cAAc,EACxB;MACA,OAAO,IAAI;IACb;IAEA,IACE,IAAI,CAACG,gBAAgB,KAAKR,MAAM,CAACG,gBAAgB,IACjDqF,EAAE,GAAG,IAAI,CAAChF,gBAAgB,EAC1B;MACA,OAAO,IAAI;IACb;IAEA,OACE,IAAI,CAACC,cAAc,KAAKT,MAAM,CAACC,gBAAgB,IAC/CuF,EAAE,GAAG,IAAI,CAAC/E,cAAc;EAE5B;EAEQ+C,QAAQA,CAACT,KAAmB,EAAQ;IAC1C,IACE,IAAI,CAACc,KAAK,KAAK1E,KAAK,CAACuC,YAAY,IACjC,IAAI,CAACwB,OAAO,CAACU,oBAAoB,IAAI,IAAI,CAAC/C,WAAW,EACrD;MACA,IAAI,CAACwD,aAAa,CAAC,CAAC;MACpB,IAAI,CAACpD,OAAO,GAAG,CAAC;MAChB,IAAI,CAACC,OAAO,GAAG,CAAC;MAChB,IAAI,CAACtB,SAAS,GAAG,CAAC;MAClB,IAAI,CAACC,SAAS,GAAG,CAAC;MAElB,IAAI,CAACsF,KAAK,CAAC,CAAC;MAEZ,IAAI,IAAI,CAAC9D,sBAAsB,GAAG,CAAC,EAAE;QACnC,IAAI,CAACC,iBAAiB,GAAGwD,UAAU,CAAC,MAAM;UACxC,IAAI,CAACM,QAAQ,CAAC,CAAC;QACjB,CAAC,EAAE,IAAI,CAAC/D,sBAAsB,CAAC;MACjC;IACF,CAAC,MAAM;MACL,MAAMoD,QAAQ,GAAG,IAAI,CAACvB,OAAO,CAACwB,WAAW,CAAC3B,KAAK,CAACoB,SAAS,CAAC;MAC1D,IAAI,CAACvE,SAAS,GAAG6E,QAAQ,CAACnB,CAAC;MAC3B,IAAI,CAACzD,SAAS,GAAG4E,QAAQ,CAAClB,CAAC;IAC7B;EACF;EAEQE,UAAUA,CAAA,EAAS;IACzB,IAAI,IAAI,CAACI,KAAK,KAAK1E,KAAK,CAAC2G,KAAK,EAAE;MAC9B,IAAI,IAAI,CAACD,UAAU,CAAC,CAAC,EAAE;QACrB,IAAI,CAAC7B,IAAI,CAAC,CAAC;MACb,CAAC,MAAM,IAAI,IAAI,CAACsB,cAAc,CAAC,CAAC,EAAE;QAChC,IAAI,CAACF,QAAQ,CAAC,CAAC;MACjB;IACF;EACF;EAEOA,QAAQA,CAACW,KAAK,GAAG,KAAK,EAAQ;IACnC,IAAI,IAAI,CAAClC,KAAK,KAAK1E,KAAK,CAAC2E,MAAM,EAAE;MAC/B,IAAI,CAACO,aAAa,CAAC,CAAC;IACtB;IAEA,KAAK,CAACe,QAAQ,CAACW,KAAK,CAAC;EACvB;EAEUC,QAAQA,CAAA,EAAS;IACzB,IAAI,CAACpD,sBAAsB,CAAC,CAAC;EAC/B;EAEUqD,OAAOA,CAAA,EAAS;IACxB,IAAI,CAACrD,sBAAsB,CAAC,CAAC;EAC/B;EAEUyB,aAAaA,CAAA,EAAS;IAC9B,IAAI,IAAI,CAACR,KAAK,KAAK1E,KAAK,CAAC2E,MAAM,EAAE;MAC/B;IACF;IAEA,IAAI,CAAC/C,MAAM,GAAG,IAAI,CAACI,KAAK;IACxB,IAAI,CAACH,MAAM,GAAG,IAAI,CAACI,KAAK;EAC1B;AACF", "ignoreList": []}