{"version": 3, "names": ["DiagonalDirections", "Directions", "MINIMAL_RECOGNIZABLE_MAGNITUDE", "Vector", "constructor", "x", "y", "_magnitude", "Math", "hypot", "isMagnitudeSufficient", "unitX", "unitY", "fromDirection", "direction", "DirectionToVectorMappings", "get", "fromVelocity", "tracker", "pointerId", "velocity", "getVelocity", "magnitude", "computeSimilarity", "vector", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "threshold", "Map", "LEFT", "RIGHT", "UP", "DOWN", "UP_RIGHT", "DOWN_RIGHT", "UP_LEFT", "DOWN_LEFT"], "sourceRoot": "../../../../src", "sources": ["web/tools/Vector.ts"], "mappings": ";;AAAA,SAASA,kBAAkB,EAAEC,UAAU,QAAQ,kBAAkB;AACjE,SAASC,8BAA8B,QAAQ,cAAc;AAG7D,eAAe,MAAMC,MAAM,CAAC;EAO1BC,WAAWA,CAACC,CAAS,EAAEC,CAAS,EAAE;IAChC,IAAI,CAACD,CAAC,GAAGA,CAAC;IACV,IAAI,CAACC,CAAC,GAAGA,CAAC;IAEV,IAAI,CAACC,UAAU,GAAGC,IAAI,CAACC,KAAK,CAAC,IAAI,CAACJ,CAAC,EAAE,IAAI,CAACC,CAAC,CAAC;IAC5C,MAAMI,qBAAqB,GACzB,IAAI,CAACH,UAAU,GAAGL,8BAA8B;IAElD,IAAI,CAACS,KAAK,GAAGD,qBAAqB,GAAG,IAAI,CAACL,CAAC,GAAG,IAAI,CAACE,UAAU,GAAG,CAAC;IACjE,IAAI,CAACK,KAAK,GAAGF,qBAAqB,GAAG,IAAI,CAACJ,CAAC,GAAG,IAAI,CAACC,UAAU,GAAG,CAAC;EACnE;EAEA,OAAOM,aAAaA,CAACC,SAA0C,EAAU;IACvE,OAAOC,yBAAyB,CAACC,GAAG,CAACF,SAAS,CAAC,IAAI,IAAIX,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;EACrE;EAEA,OAAOc,YAAYA,CAACC,OAAuB,EAAEC,SAAiB,EAAE;IAC9D,MAAMC,QAAQ,GAAGF,OAAO,CAACG,WAAW,CAACF,SAAS,CAAC;IAC/C,OAAO,IAAIhB,MAAM,CAACiB,QAAQ,CAACf,CAAC,EAAEe,QAAQ,CAACd,CAAC,CAAC;EAC3C;EAEA,IAAWgB,SAASA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACf,UAAU;EACxB;EAEAgB,iBAAiBA,CAACC,MAAc,EAAE;IAChC,OAAO,IAAI,CAACb,KAAK,GAAGa,MAAM,CAACb,KAAK,GAAG,IAAI,CAACC,KAAK,GAAGY,MAAM,CAACZ,KAAK;EAC9D;EAEAa,SAASA,CAACD,MAAc,EAAEE,SAAiB,EAAE;IAC3C,OAAO,IAAI,CAACH,iBAAiB,CAACC,MAAM,CAAC,GAAGE,SAAS;EACnD;AACF;AAEA,MAAMX,yBAAyB,GAAG,IAAIY,GAAG,CAGvC,CACA,CAAC1B,UAAU,CAAC2B,IAAI,EAAE,IAAIzB,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EACpC,CAACF,UAAU,CAAC4B,KAAK,EAAE,IAAI1B,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EACpC,CAACF,UAAU,CAAC6B,EAAE,EAAE,IAAI3B,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAClC,CAACF,UAAU,CAAC8B,IAAI,EAAE,IAAI5B,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAEnC,CAACH,kBAAkB,CAACgC,QAAQ,EAAE,IAAI7B,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAChD,CAACH,kBAAkB,CAACiC,UAAU,EAAE,IAAI9B,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EACjD,CAACH,kBAAkB,CAACkC,OAAO,EAAE,IAAI/B,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAChD,CAACH,kBAAkB,CAACmC,SAAS,EAAE,IAAIhC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAClD,CAAC", "ignoreList": []}