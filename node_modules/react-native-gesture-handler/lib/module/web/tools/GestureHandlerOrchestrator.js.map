{"version": 3, "names": ["PointerType", "State", "PointerTracker", "GestureHandlerOrchestrator", "gestureHandlers", "awaitingHandlers", "awaitingHandlersTags", "Set", "handlingChangeSemaphore", "activationIndex", "constructor", "scheduleFinishedHandlersCleanup", "cleanupFinishedHandlers", "<PERSON><PERSON><PERSON><PERSON>", "handler", "reset", "active", "awaiting", "Number", "MAX_VALUE", "removeHandlerFromOrchestrator", "indexInGestureHandlers", "indexOf", "indexInAwaitingHandlers", "splice", "delete", "handlerTag", "handlersToRemove", "i", "length", "isFinished", "state", "add", "filter", "has", "hasOtherHandlerToWaitFor", "hasToWaitFor", "<PERSON><PERSON><PERSON><PERSON>", "shouldHandlerWaitForOther", "some", "shouldBeCancelledByFinishedHandler", "shouldBeCancelled", "END", "tryActivate", "cancel", "add<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handlerState", "CANCELLED", "FAILED", "shouldActivate", "makeActive", "ACTIVE", "fail", "BEGAN", "shouldBeCancelledBy", "shouldHandlerBeCancelledBy", "cleanupAwaitingHandlers", "should<PERSON>ait", "onHandlerStateChange", "newState", "oldState", "sendIfDisabled", "enabled", "sendEvent", "UNDETERMINED", "includes", "currentState", "shouldResetProgress", "push", "recordHandlerIfNotPresent", "MAX_SAFE_INTEGER", "shouldWaitForHandlerFailure", "shouldRequireToWaitForFailure", "canRunSimultaneously", "gh1", "gh2", "shouldRecognizeSimultaneously", "shouldBeCancelledByOther", "handlerPointers", "getTrackedPointersID", "otherPointers", "shareCommonPointers", "delegate", "view", "checkOverlap", "isPointerWithinBothBounds", "pointer", "point", "tracker", "getLastAbsoluteCoords", "isPointerInBounds", "cancelMouseAndPenGestures", "<PERSON><PERSON><PERSON><PERSON>", "for<PERSON>ach", "pointerType", "MOUSE", "STYLUS", "resetTracker", "instance", "_instance"], "sourceRoot": "../../../../src", "sources": ["web/tools/GestureHandlerOrchestrator.ts"], "mappings": ";;AAAA,SAASA,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,KAAK,QAAQ,aAAa;AAGnC,OAAOC,cAAc,MAAM,kBAAkB;AAE7C,eAAe,MAAMC,0BAA0B,CAAC;EAGtCC,eAAe,GAAsB,EAAE;EACvCC,gBAAgB,GAAsB,EAAE;EACxCC,oBAAoB,GAAgB,IAAIC,GAAG,CAAC,CAAC;EAE7CC,uBAAuB,GAAG,CAAC;EAC3BC,eAAe,GAAG,CAAC;;EAE3B;EACA;EACQC,WAAWA,CAAA,EAAG,CAAC;EAEfC,+BAA+BA,CAAA,EAAS;IAC9C,IAAI,IAAI,CAACH,uBAAuB,KAAK,CAAC,EAAE;MACtC,IAAI,CAACI,uBAAuB,CAAC,CAAC;IAChC;EACF;EAEQC,YAAYA,CAACC,OAAwB,EAAQ;IACnDA,OAAO,CAACC,KAAK,CAAC,CAAC;IACfD,OAAO,CAACE,MAAM,GAAG,KAAK;IACtBF,OAAO,CAACG,QAAQ,GAAG,KAAK;IACxBH,OAAO,CAACL,eAAe,GAAGS,MAAM,CAACC,SAAS;EAC5C;EAEOC,6BAA6BA,CAACN,OAAwB,EAAQ;IACnE,MAAMO,sBAAsB,GAAG,IAAI,CAACjB,eAAe,CAACkB,OAAO,CAACR,OAAO,CAAC;IACpE,MAAMS,uBAAuB,GAAG,IAAI,CAAClB,gBAAgB,CAACiB,OAAO,CAACR,OAAO,CAAC;IAEtE,IAAIO,sBAAsB,IAAI,CAAC,EAAE;MAC/B,IAAI,CAACjB,eAAe,CAACoB,MAAM,CAACH,sBAAsB,EAAE,CAAC,CAAC;IACxD;IAEA,IAAIE,uBAAuB,IAAI,CAAC,EAAE;MAChC,IAAI,CAAClB,gBAAgB,CAACmB,MAAM,CAACD,uBAAuB,EAAE,CAAC,CAAC;MACxD,IAAI,CAACjB,oBAAoB,CAACmB,MAAM,CAACX,OAAO,CAACY,UAAU,CAAC;IACtD;EACF;EAEQd,uBAAuBA,CAAA,EAAS;IACtC,MAAMe,gBAAgB,GAAG,IAAIpB,GAAG,CAAkB,CAAC;IAEnD,KAAK,IAAIqB,CAAC,GAAG,IAAI,CAACxB,eAAe,CAACyB,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;MACzD,MAAMd,OAAO,GAAG,IAAI,CAACV,eAAe,CAACwB,CAAC,CAAC;MAEvC,IAAI,IAAI,CAACE,UAAU,CAAChB,OAAO,CAACiB,KAAK,CAAC,IAAI,CAACjB,OAAO,CAACG,QAAQ,EAAE;QACvD,IAAI,CAACJ,YAAY,CAACC,OAAO,CAAC;QAC1Ba,gBAAgB,CAACK,GAAG,CAAClB,OAAO,CAAC;MAC/B;IACF;IAEA,IAAI,CAACV,eAAe,GAAG,IAAI,CAACA,eAAe,CAAC6B,MAAM,CAC/CnB,OAAO,IAAK,CAACa,gBAAgB,CAACO,GAAG,CAACpB,OAAO,CAC5C,CAAC;EACH;EAEQqB,wBAAwBA,CAACrB,OAAwB,EAAW;IAClE,MAAMsB,YAAY,GAAIC,YAA6B,IAAK;MACtD,OACE,CAAC,IAAI,CAACP,UAAU,CAACO,YAAY,CAACN,KAAK,CAAC,IACpC,IAAI,CAACO,yBAAyB,CAACxB,OAAO,EAAEuB,YAAY,CAAC;IAEzD,CAAC;IAED,OAAO,IAAI,CAACjC,eAAe,CAACmC,IAAI,CAACH,YAAY,CAAC;EAChD;EAEQI,kCAAkCA,CACxC1B,OAAwB,EACf;IACT,MAAM2B,iBAAiB,GAAIJ,YAA6B,IAAK;MAC3D,OACE,IAAI,CAACC,yBAAyB,CAACxB,OAAO,EAAEuB,YAAY,CAAC,IACrDA,YAAY,CAACN,KAAK,KAAK9B,KAAK,CAACyC,GAAG;IAEpC,CAAC;IAED,OAAO,IAAI,CAACtC,eAAe,CAACmC,IAAI,CAACE,iBAAiB,CAAC;EACrD;EAEQE,WAAWA,CAAC7B,OAAwB,EAAQ;IAClD,IAAI,IAAI,CAAC0B,kCAAkC,CAAC1B,OAAO,CAAC,EAAE;MACpDA,OAAO,CAAC8B,MAAM,CAAC,CAAC;MAChB;IACF;IAEA,IAAI,IAAI,CAACT,wBAAwB,CAACrB,OAAO,CAAC,EAAE;MAC1C,IAAI,CAAC+B,kBAAkB,CAAC/B,OAAO,CAAC;MAChC;IACF;IAEA,MAAMgC,YAAY,GAAGhC,OAAO,CAACiB,KAAK;IAElC,IAAIe,YAAY,KAAK7C,KAAK,CAAC8C,SAAS,IAAID,YAAY,KAAK7C,KAAK,CAAC+C,MAAM,EAAE;MACrE;IACF;IAEA,IAAI,IAAI,CAACC,cAAc,CAACnC,OAAO,CAAC,EAAE;MAChC,IAAI,CAACoC,UAAU,CAACpC,OAAO,CAAC;MACxB;IACF;IAEA,IAAIgC,YAAY,KAAK7C,KAAK,CAACkD,MAAM,EAAE;MACjCrC,OAAO,CAACsC,IAAI,CAAC,CAAC;MACd;IACF;IAEA,IAAIN,YAAY,KAAK7C,KAAK,CAACoD,KAAK,EAAE;MAChCvC,OAAO,CAAC8B,MAAM,CAAC,CAAC;IAClB;EACF;EAEQK,cAAcA,CAACnC,OAAwB,EAAW;IACxD,MAAMwC,mBAAmB,GAAIjB,YAA6B,IAAK;MAC7D,OAAO,IAAI,CAACkB,0BAA0B,CAACzC,OAAO,EAAEuB,YAAY,CAAC;IAC/D,CAAC;IAED,OAAO,CAAC,IAAI,CAACjC,eAAe,CAACmC,IAAI,CAACe,mBAAmB,CAAC;EACxD;EAEQE,uBAAuBA,CAAC1C,OAAwB,EAAQ;IAC9D,MAAM2C,UAAU,GAAIpB,YAA6B,IAAK;MACpD,OACE,CAACA,YAAY,CAACpB,QAAQ,IACtB,IAAI,CAACqB,yBAAyB,CAACD,YAAY,EAAEvB,OAAO,CAAC;IAEzD,CAAC;IAED,KAAK,MAAMuB,YAAY,IAAI,IAAI,CAAChC,gBAAgB,EAAE;MAChD,IAAIoD,UAAU,CAACpB,YAAY,CAAC,EAAE;QAC5B,IAAI,CAACxB,YAAY,CAACwB,YAAY,CAAC;QAC/B,IAAI,CAAC/B,oBAAoB,CAACmB,MAAM,CAACY,YAAY,CAACX,UAAU,CAAC;MAC3D;IACF;IAEA,IAAI,CAACrB,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAAC4B,MAAM,CAAEI,YAAY,IAChE,IAAI,CAAC/B,oBAAoB,CAAC4B,GAAG,CAACG,YAAY,CAACX,UAAU,CACvD,CAAC;EACH;EAEOgC,oBAAoBA,CACzB5C,OAAwB,EACxB6C,QAAe,EACfC,QAAe,EACfC,cAAwB,EAClB;IACN,IAAI,CAAC/C,OAAO,CAACgD,OAAO,IAAI,CAACD,cAAc,EAAE;MACvC;IACF;IAEA,IAAI,CAACrD,uBAAuB,IAAI,CAAC;IAEjC,IAAI,IAAI,CAACsB,UAAU,CAAC6B,QAAQ,CAAC,EAAE;MAC7B,KAAK,MAAMtB,YAAY,IAAI,IAAI,CAAChC,gBAAgB,EAAE;QAChD,IACE,CAAC,IAAI,CAACiC,yBAAyB,CAACD,YAAY,EAAEvB,OAAO,CAAC,IACtD,CAAC,IAAI,CAACR,oBAAoB,CAAC4B,GAAG,CAACG,YAAY,CAACX,UAAU,CAAC,EACvD;UACA;QACF;QAEA,IAAIiC,QAAQ,KAAK1D,KAAK,CAACyC,GAAG,EAAE;UAC1B,IAAI,CAACC,WAAW,CAACN,YAAY,CAAC;UAC9B;QACF;QAEAA,YAAY,CAACO,MAAM,CAAC,CAAC;QAErB,IAAIP,YAAY,CAACN,KAAK,KAAK9B,KAAK,CAACyC,GAAG,EAAE;UACpC;UACA;UACA;UACA;UACAL,YAAY,CAAC0B,SAAS,CAAC9D,KAAK,CAAC8C,SAAS,EAAE9C,KAAK,CAACoD,KAAK,CAAC;QACtD;QAEAhB,YAAY,CAACpB,QAAQ,GAAG,KAAK;MAC/B;IACF;IAEA,IAAI0C,QAAQ,KAAK1D,KAAK,CAACkD,MAAM,EAAE;MAC7B,IAAI,CAACR,WAAW,CAAC7B,OAAO,CAAC;IAC3B,CAAC,MAAM,IAAI8C,QAAQ,KAAK3D,KAAK,CAACkD,MAAM,IAAIS,QAAQ,KAAK3D,KAAK,CAACyC,GAAG,EAAE;MAC9D,IAAI5B,OAAO,CAACE,MAAM,EAAE;QAClBF,OAAO,CAACiD,SAAS,CAACJ,QAAQ,EAAEC,QAAQ,CAAC;MACvC,CAAC,MAAM,IACLA,QAAQ,KAAK3D,KAAK,CAACkD,MAAM,KACxBQ,QAAQ,KAAK1D,KAAK,CAAC8C,SAAS,IAAIY,QAAQ,KAAK1D,KAAK,CAAC+C,MAAM,CAAC,EAC3D;QACAlC,OAAO,CAACiD,SAAS,CAACJ,QAAQ,EAAE1D,KAAK,CAACoD,KAAK,CAAC;MAC1C;IACF,CAAC,MAAM,IACLO,QAAQ,KAAK3D,KAAK,CAAC+D,YAAY,IAC/BL,QAAQ,KAAK1D,KAAK,CAAC8C,SAAS,EAC5B;MACAjC,OAAO,CAACiD,SAAS,CAACJ,QAAQ,EAAEC,QAAQ,CAAC;IACvC;IAEA,IAAI,CAACpD,uBAAuB,IAAI,CAAC;IAEjC,IAAI,CAACG,+BAA+B,CAAC,CAAC;IAEtC,IAAI,CAAC,IAAI,CAACN,gBAAgB,CAAC4D,QAAQ,CAACnD,OAAO,CAAC,EAAE;MAC5C,IAAI,CAAC0C,uBAAuB,CAAC1C,OAAO,CAAC;IACvC;EACF;EAEQoC,UAAUA,CAACpC,OAAwB,EAAQ;IACjD,MAAMoD,YAAY,GAAGpD,OAAO,CAACiB,KAAK;IAElCjB,OAAO,CAACE,MAAM,GAAG,IAAI;IACrBF,OAAO,CAACqD,mBAAmB,GAAG,IAAI;IAClCrD,OAAO,CAACL,eAAe,GAAG,IAAI,CAACA,eAAe,EAAE;IAEhD,KAAK,IAAImB,CAAC,GAAG,IAAI,CAACxB,eAAe,CAACyB,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;MACzD,IAAI,IAAI,CAAC2B,0BAA0B,CAAC,IAAI,CAACnD,eAAe,CAACwB,CAAC,CAAC,EAAEd,OAAO,CAAC,EAAE;QACrE,IAAI,CAACV,eAAe,CAACwB,CAAC,CAAC,CAACgB,MAAM,CAAC,CAAC;MAClC;IACF;IAEA,KAAK,MAAMP,YAAY,IAAI,IAAI,CAAChC,gBAAgB,EAAE;MAChD,IAAI,IAAI,CAACkD,0BAA0B,CAAClB,YAAY,EAAEvB,OAAO,CAAC,EAAE;QAC1DuB,YAAY,CAACpB,QAAQ,GAAG,KAAK;MAC/B;IACF;IAEAH,OAAO,CAACiD,SAAS,CAAC9D,KAAK,CAACkD,MAAM,EAAElD,KAAK,CAACoD,KAAK,CAAC;IAE5C,IAAIa,YAAY,KAAKjE,KAAK,CAACkD,MAAM,EAAE;MACjCrC,OAAO,CAACiD,SAAS,CAAC9D,KAAK,CAACyC,GAAG,EAAEzC,KAAK,CAACkD,MAAM,CAAC;MAC1C,IAAIe,YAAY,KAAKjE,KAAK,CAACyC,GAAG,EAAE;QAC9B5B,OAAO,CAACiD,SAAS,CAAC9D,KAAK,CAAC+D,YAAY,EAAE/D,KAAK,CAACyC,GAAG,CAAC;MAClD;IACF;IAEA,IAAI,CAAC5B,OAAO,CAACG,QAAQ,EAAE;MACrB;IACF;IAEAH,OAAO,CAACG,QAAQ,GAAG,KAAK;IAExB,IAAI,CAACZ,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAAC4B,MAAM,CACjDI,YAAY,IAAKA,YAAY,KAAKvB,OACrC,CAAC;EACH;EAEQ+B,kBAAkBA,CAAC/B,OAAwB,EAAQ;IACzD,IAAI,IAAI,CAACT,gBAAgB,CAAC4D,QAAQ,CAACnD,OAAO,CAAC,EAAE;MAC3C;IACF;IAEA,IAAI,CAACT,gBAAgB,CAAC+D,IAAI,CAACtD,OAAO,CAAC;IACnC,IAAI,CAACR,oBAAoB,CAAC0B,GAAG,CAAClB,OAAO,CAACY,UAAU,CAAC;IAEjDZ,OAAO,CAACG,QAAQ,GAAG,IAAI;IACvBH,OAAO,CAACL,eAAe,GAAG,IAAI,CAACA,eAAe,EAAE;EAClD;EAEO4D,yBAAyBA,CAACvD,OAAwB,EAAQ;IAC/D,IAAI,IAAI,CAACV,eAAe,CAAC6D,QAAQ,CAACnD,OAAO,CAAC,EAAE;MAC1C;IACF;IAEA,IAAI,CAACV,eAAe,CAACgE,IAAI,CAACtD,OAAO,CAAC;IAElCA,OAAO,CAACE,MAAM,GAAG,KAAK;IACtBF,OAAO,CAACG,QAAQ,GAAG,KAAK;IACxBH,OAAO,CAACL,eAAe,GAAGS,MAAM,CAACoD,gBAAgB;EACnD;EAEQhC,yBAAyBA,CAC/BxB,OAAwB,EACxBuB,YAA6B,EACpB;IACT,OACEvB,OAAO,KAAKuB,YAAY,KACvBvB,OAAO,CAACyD,2BAA2B,CAAClC,YAAY,CAAC,IAChDA,YAAY,CAACmC,6BAA6B,CAAC1D,OAAO,CAAC,CAAC;EAE1D;EAEQ2D,oBAAoBA,CAC1BC,GAAoB,EACpBC,GAAoB,EACX;IACT,OACED,GAAG,KAAKC,GAAG,IACXD,GAAG,CAACE,6BAA6B,CAACD,GAAG,CAAC,IACtCA,GAAG,CAACC,6BAA6B,CAACF,GAAG,CAAC;EAE1C;EAEQnB,0BAA0BA,CAChCzC,OAAwB,EACxBuB,YAA6B,EACpB;IACT,IAAI,IAAI,CAACoC,oBAAoB,CAAC3D,OAAO,EAAEuB,YAAY,CAAC,EAAE;MACpD,OAAO,KAAK;IACd;IAEA,IAAIvB,OAAO,CAACG,QAAQ,IAAIH,OAAO,CAACiB,KAAK,KAAK9B,KAAK,CAACkD,MAAM,EAAE;MACtD,OAAOrC,OAAO,CAAC+D,wBAAwB,CAACxC,YAAY,CAAC;IACvD;IAEA,MAAMyC,eAAyB,GAAGhE,OAAO,CAACiE,oBAAoB,CAAC,CAAC;IAChE,MAAMC,aAAuB,GAAG3C,YAAY,CAAC0C,oBAAoB,CAAC,CAAC;IAEnE,IACE,CAAC7E,cAAc,CAAC+E,mBAAmB,CAACH,eAAe,EAAEE,aAAa,CAAC,IACnElE,OAAO,CAACoE,QAAQ,CAACC,IAAI,KAAK9C,YAAY,CAAC6C,QAAQ,CAACC,IAAI,EACpD;MACA,OAAO,IAAI,CAACC,YAAY,CAACtE,OAAO,EAAEuB,YAAY,CAAC;IACjD;IAEA,OAAO,IAAI;EACb;EAEQ+C,YAAYA,CAClBtE,OAAwB,EACxBuB,YAA6B,EACpB;IACT;IACA;IACA;;IAEA;;IAEA,MAAMgD,yBAAyB,GAAIC,OAAe,IAAK;MACrD,MAAMC,KAAK,GAAGzE,OAAO,CAAC0E,OAAO,CAACC,qBAAqB,CAACH,OAAO,CAAC;MAE5D,OACExE,OAAO,CAACoE,QAAQ,CAACQ,iBAAiB,CAACH,KAAK,CAAC,IACzClD,YAAY,CAAC6C,QAAQ,CAACQ,iBAAiB,CAACH,KAAK,CAAC;IAElD,CAAC;IAED,OAAOzE,OAAO,CAACiE,oBAAoB,CAAC,CAAC,CAACxC,IAAI,CAAC8C,yBAAyB,CAAC;EACvE;EAEQvD,UAAUA,CAACC,KAAY,EAAW;IACxC,OACEA,KAAK,KAAK9B,KAAK,CAACyC,GAAG,IAAIX,KAAK,KAAK9B,KAAK,CAAC+C,MAAM,IAAIjB,KAAK,KAAK9B,KAAK,CAAC8C,SAAS;EAE9E;;EAEA;EACA;EACA;EACA;EACA;EACA;EACO4C,yBAAyBA,CAACC,cAA+B,EAAQ;IACtE,IAAI,CAACxF,eAAe,CAACyF,OAAO,CAAE/E,OAAwB,IAAK;MACzD,IACEA,OAAO,CAACgF,WAAW,KAAK9F,WAAW,CAAC+F,KAAK,IACzCjF,OAAO,CAACgF,WAAW,KAAK9F,WAAW,CAACgG,MAAM,EAC1C;QACA;MACF;MAEA,IAAIlF,OAAO,KAAK8E,cAAc,EAAE;QAC9B9E,OAAO,CAAC8B,MAAM,CAAC,CAAC;MAClB,CAAC,MAAM;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA9B,OAAO,CAAC0E,OAAO,CAACS,YAAY,CAAC,CAAC;MAChC;IACF,CAAC,CAAC;EACJ;EAEA,WAAkBC,QAAQA,CAAA,EAA+B;IACvD,IAAI,CAAC/F,0BAA0B,CAACgG,SAAS,EAAE;MACzChG,0BAA0B,CAACgG,SAAS,GAAG,IAAIhG,0BAA0B,CAAC,CAAC;IACzE;IAEA,OAAOA,0BAA0B,CAACgG,SAAS;EAC7C;AACF", "ignoreList": []}