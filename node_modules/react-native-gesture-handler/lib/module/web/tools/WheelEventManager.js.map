{"version": 3, "names": ["EventManager", "EventTypes", "PointerType", "WheelEventManager", "wheelDelta", "x", "y", "reset<PERSON><PERSON><PERSON>", "_event", "wheelCallback", "event", "deltaX", "deltaY", "adaptedEvent", "mapEvent", "onWheel", "registerListeners", "view", "addEventListener", "unregisterListeners", "removeEventListener", "clientX", "clientY", "offsetX", "offsetY", "pointerId", "eventType", "MOVE", "pointerType", "OTHER", "time", "timeStamp", "wheelDeltaY", "resetManager"], "sourceRoot": "../../../../src", "sources": ["web/tools/WheelEventManager.ts"], "mappings": ";;AAAA,OAAOA,YAAY,MAAM,gBAAgB;AACzC,SAAuBC,UAAU,QAAQ,eAAe;AACxD,SAASC,WAAW,QAAQ,mBAAmB;AAE/C,eAAe,MAAMC,iBAAiB,SAASH,YAAY,CAAc;EAC/DI,UAAU,GAAG;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC;EAE3BC,UAAU,GAAIC,MAAoB,IAAK;IAC7C,IAAI,CAACJ,UAAU,GAAG;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;EAClC,CAAC;EAEOG,aAAa,GAAIC,KAAiB,IAAK;IAC7C,IAAI,CAACN,UAAU,CAACC,CAAC,IAAIK,KAAK,CAACC,MAAM;IACjC,IAAI,CAACP,UAAU,CAACE,CAAC,IAAII,KAAK,CAACE,MAAM;IAEjC,MAAMC,YAAY,GAAG,IAAI,CAACC,QAAQ,CAACJ,KAAK,CAAC;IACzC,IAAI,CAACK,OAAO,CAACF,YAAY,CAAC;EAC5B,CAAC;EAEMG,iBAAiBA,CAAA,EAAS;IAC/B,IAAI,CAACC,IAAI,CAACC,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAACX,UAAU,CAAC;IAC1D,IAAI,CAACU,IAAI,CAACC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACT,aAAa,CAAC;EACzD;EAEOU,mBAAmBA,CAAA,EAAS;IACjC,IAAI,CAACF,IAAI,CAACG,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAACb,UAAU,CAAC;IAC7D,IAAI,CAACU,IAAI,CAACG,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACX,aAAa,CAAC;EAC5D;EAEUK,QAAQA,CAACJ,KAAiB,EAAgB;IAClD,OAAO;MACLL,CAAC,EAAEK,KAAK,CAACW,OAAO,GAAG,IAAI,CAACjB,UAAU,CAACC,CAAC;MACpCC,CAAC,EAAEI,KAAK,CAACY,OAAO,GAAG,IAAI,CAAClB,UAAU,CAACE,CAAC;MACpCiB,OAAO,EAAEb,KAAK,CAACa,OAAO,GAAGb,KAAK,CAACC,MAAM;MACrCa,OAAO,EAAEd,KAAK,CAACc,OAAO,GAAGd,KAAK,CAACE,MAAM;MACrCa,SAAS,EAAE,CAAC,CAAC;MACbC,SAAS,EAAEzB,UAAU,CAAC0B,IAAI;MAC1BC,WAAW,EAAE1B,WAAW,CAAC2B,KAAK;MAC9BC,IAAI,EAAEpB,KAAK,CAACqB,SAAS;MACrB;MACAC,WAAW,EAAEtB,KAAK,CAACsB;IACrB,CAAC;EACH;EAEOC,YAAYA,CAAA,EAAS;IAC1B,KAAK,CAACA,YAAY,CAAC,CAAC;EACtB;AACF", "ignoreList": []}