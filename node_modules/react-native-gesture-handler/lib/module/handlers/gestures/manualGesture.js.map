{"version": 3, "names": ["ContinousBaseGesture", "changeEventCalculator", "current", "_previous", "ManualGesture", "constructor", "handler<PERSON>ame", "onChange", "callback", "handlers"], "sourceRoot": "../../../../src", "sources": ["handlers/gestures/manualGesture.ts"], "mappings": ";;AACA,SAASA,oBAAoB,QAAQ,WAAW;AAEhD,SAASC,qBAAqBA,CAC5BC,OAAkD,EAClDC,SAAqD,EACrD;EACA,SAAS;;EACT,OAAOD,OAAO;AAChB;AAEA,OAAO,MAAME,aAAa,SAASJ,oBAAoB,CAGrD;EACAK,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,WAAW,GAAG,sBAAsB;EAC3C;EAEAC,QAAQA,CACNC,QAAoE,EACpE;IACA;IACA,IAAI,CAACC,QAAQ,CAACR,qBAAqB,GAAGA,qBAAqB;IAC3D,OAAO,KAAK,CAACM,QAAQ,CAACC,QAAQ,CAAC;EACjC;AACF", "ignoreList": []}