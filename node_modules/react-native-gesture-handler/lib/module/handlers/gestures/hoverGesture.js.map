{"version": 3, "names": ["ContinousBaseGesture", "HoverEffect", "hoverGestureHandlerProps", "changeEventCalculator", "current", "previous", "changePayload", "undefined", "changeX", "x", "changeY", "y", "HoverGesture", "config", "constructor", "handler<PERSON>ame", "effect", "hoverEffect", "onChange", "callback", "handlers"], "sourceRoot": "../../../../src", "sources": ["handlers/gestures/hoverGesture.ts"], "mappings": ";;AAAA,SAA4BA,oBAAoB,QAAQ,WAAW;AASnE,WAAYC,WAAW,0BAAXA,WAAW;EAAXA,WAAW,CAAXA,WAAW;EAAXA,WAAW,CAAXA,WAAW;EAAXA,WAAW,CAAXA,WAAW;EAAA,OAAXA,WAAW;AAAA;AAUvB,OAAO,MAAMC,wBAAwB,GAAG,CAAC,aAAa,CAAU;AAEhE,SAASC,qBAAqBA,CAC5BC,OAA4D,EAC5DC,QAA8D,EAC9D;EACA,SAAS;;EACT,IAAIC,aAA6C;EACjD,IAAID,QAAQ,KAAKE,SAAS,EAAE;IAC1BD,aAAa,GAAG;MACdE,OAAO,EAAEJ,OAAO,CAACK,CAAC;MAClBC,OAAO,EAAEN,OAAO,CAACO;IACnB,CAAC;EACH,CAAC,MAAM;IACLL,aAAa,GAAG;MACdE,OAAO,EAAEJ,OAAO,CAACK,CAAC,GAAGJ,QAAQ,CAACI,CAAC;MAC/BC,OAAO,EAAEN,OAAO,CAACO,CAAC,GAAGN,QAAQ,CAACM;IAChC,CAAC;EACH;EAEA,OAAO;IAAE,GAAGP,OAAO;IAAE,GAAGE;EAAc,CAAC;AACzC;AAEA,OAAO,MAAMM,YAAY,SAASZ,oBAAoB,CAGpD;EACOa,MAAM,GAA2C,CAAC,CAAC;EAE1DC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,WAAW,GAAG,qBAAqB;EAC1C;;EAEA;AACF;AACA;AACA;EACEC,MAAMA,CAACA,MAAmB,EAAE;IAC1B,IAAI,CAACH,MAAM,CAACI,WAAW,GAAGD,MAAM;IAChC,OAAO,IAAI;EACb;EAEAE,QAAQA,CACNC,QAIS,EACT;IACA;IACA,IAAI,CAACC,QAAQ,CAACjB,qBAAqB,GAAGA,qBAAqB;IAC3D,OAAO,KAAK,CAACe,QAAQ,CAACC,QAAQ,CAAC;EACjC;AACF", "ignoreList": []}